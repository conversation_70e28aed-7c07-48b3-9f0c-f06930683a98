import { CountryCode } from '@/contexts/checkout/checkout-context-types';
import { UseFormRegister } from 'node_modules/react-hook-form/dist/types/form';
import { useState } from 'react';
import { FieldErrors } from 'react-hook-form';
import InputMask from 'react-input-mask';
import { CheckoutFormData } from '../checkout/types';
import { InputMessageError } from '../InputMessageError';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { ScrollArea } from '../ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';

interface PhoneInputProps {
  selectId: string;
  inputId: string;
  countryCodes: CountryCode[];
  selectedCountry: CountryCode;
  setSelectedCountryCode: React.Dispatch<React.SetStateAction<string>>;
  type: 'mobile' | 'phone';
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  disabled?: boolean;
  suitable?: boolean
}

export const PhoneInput = ({
  selectId,
  inputId,
  countryCodes,
  selectedCountry,
  setSelectedCountryCode,
  type,
  register,
  errors,
  suitable = true,
  disabled = false
}: PhoneInputProps) => {
  const [phoneType, setPhoneType] = useState<'mobile' | 'phone'>(type);

  return <div>
    <Label htmlFor="phone">Telefone</Label>
    <div className="flex gap-2">
      <Select
        onValueChange={(value) => {
          const event = { target: { value } };
          register(selectId as keyof CheckoutFormData).onChange(event);
          setSelectedCountryCode(value);
        }}
        defaultValue={selectedCountry.code}
        disabled={disabled}
      >
        <SelectTrigger className="w-[100px]">
          <SelectValue>
            <div className="flex items-center gap-2">
              <span className="text-lg">{selectedCountry.flag}</span>
              <span>{selectedCountry.code}</span>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <ScrollArea className="max-h-[200px]">
            {countryCodes.map((country) => (
              <SelectItem key={country.code} value={country.code}>
                <div className="flex items-center gap-2">
                  <span className="text-lg">{country.flag}</span>
                  <span>{country.code}</span>
                </div>
              </SelectItem>
            ))}
          </ScrollArea>
        </SelectContent>
      </Select>
      {!!suitable && (
        <Select
          onValueChange={(value) => {
            const event = { target: { value } };
            register(selectId as keyof CheckoutFormData).onChange(event);
            setPhoneType(value as 'mobile' | 'phone');
          }}
          defaultValue={phoneType}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue>
              {phoneType === 'mobile' ? 'Celular' : 'Telefone'}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="mobile">Celular</SelectItem>
            <SelectItem value="phone">Telefone</SelectItem>
          </SelectContent>
        </Select>
      )}
      <InputMask
        mask={selectedCountry.mask[phoneType]}
        maskChar={null}
        {...register(inputId as keyof CheckoutFormData, {
          pattern: {
            value: selectedCountry.pattern,
            message: "Telefone inválido para o país selecionado"
          },
          required: {
            value: true,
            message: "Telefone é obrigatório"
          }
        })}
        placeholder={selectedCountry.placeholder[phoneType]}
        disabled={disabled}
      >
        {({ inputProps }) => (
          <Input
            // className={`input-class ${errors[inputId] ? 'border-red-500' : ''}`}
            {...inputProps}
            id={inputId}
            {...register(inputId as keyof CheckoutFormData)}
            placeholder={selectedCountry.placeholder[phoneType]}
            disabled={disabled}
          />
        )}
      </InputMask>
    </div>
    <InputMessageError error={errors[inputId]?.message} />
  </div>
}
