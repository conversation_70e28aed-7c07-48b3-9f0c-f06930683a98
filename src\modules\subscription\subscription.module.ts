import { Module } from '@nestjs/common';

import { PlanModule } from 'src/modules/plan/plan.module';
import { PagarmeService } from '../pagarme/pagarme.service';
import { SubscriptionRepository } from './subscription.repository';
import { SubscriptionController } from './subscription.controller';
@Module({
  imports: [
    PlanModule
  ],
  controllers: [SubscriptionController],
  providers: [SubscriptionRepository, PagarmeService],
  exports: [SubscriptionRepository],
})
export class SubscriptionModule {
  static forRoot(options: { isGlobal: boolean } = { isGlobal: false }) {
    return {
      module: SubscriptionModule,
      global: options.isGlobal,
    };
  }
}
