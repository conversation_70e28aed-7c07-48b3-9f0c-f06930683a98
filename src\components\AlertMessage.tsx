import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, CheckCircle, AlertTriangle, Info, MessageSquare } from "lucide-react";

type AlertVariant = "destructive" | "default" | "success" | "warning" | "info";

interface AlertMessageProps {
  title: string;
  message: string | string[];
  variant?: AlertVariant;
  action?: React.ReactNode;
  icon?: React.ReactNode;
}

const getIcon = (variant: AlertVariant) => {
  switch (variant) {
    case "success":
      return CheckCircle;
    case "warning":
      return AlertTriangle;
    case "info":
      return Info;
    case "default":
      return MessageSquare;
    case "destructive":
    default:
      return AlertCircle;
  }
};

export function AlertMessage({ title, message, variant = "destructive", action, icon }: AlertMessageProps) {
  const IconComponent = getIcon(variant);

  return (
    <Alert variant={variant} className="mb-4">
      <div className="flex items-center gap-2">
        {icon && <div className="flex items-center gap-2 mr-2">{icon}</div>}
        {!icon && <IconComponent className="h-6 w-6 mr-2" />}
        <div className="flex-1">
          <AlertTitle className="font-semibold text-md">{title}</AlertTitle>
          <AlertDescription className="mt-1 leading-relaxed">
            {message instanceof Array ? message.map((item, index) => (
              <p key={index}>{item}</p>
            )) : message}
          </AlertDescription>
        </div>
        {action}
      </div>
    </Alert>);
}