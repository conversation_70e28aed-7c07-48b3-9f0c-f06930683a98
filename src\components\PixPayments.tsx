import { QRCodeSVG } from "qrcode.react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Card } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";
import { ptBR } from "date-fns/locale";
import { formatDate } from "date-fns";

interface PixPaymentsModalProps {
  isOpen: boolean;
  payment: any;
  onClose: () => void;
}

const handleCopyQRCode = (qrCode: string) => {
  navigator.clipboard.writeText(qrCode);
  toast.success("QR Code copiado com sucesso!");
};
const PixPayments = ({ isOpen, onClose, payment }: PixPaymentsModalProps) => {
  if (!isOpen) return null;
  const { pix } = payment || {};
  if (!pix) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="max-w-xl p-0 bg-white rounded-lg border border-gray-300"
        aria-describedby={undefined}
      >
        <Card className="p-6 text-center space-y-4">
          <img
            src="/qi-plus-brand.png"
            alt="Logo"
            className="h-10 mx-auto"
          />

          <h2 className="text-lg font-semibold">
            Pagar {formatCurrency(pix.amount)} para completar{" "}
            {payment.isUpdated ? "sua atualização !" : "sua assinatura !"}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Abra o app do seu banco, copie e cole este código QR para fazer o
            pagamento:
          </p>

          <div className="flex justify-center">
            <QRCodeSVG
              value={pix.qrCode}
              size={220}
              level="M"
              className="border-4 border-[#EA1C78] rounded-md"
              marginSize={2}
              imageSettings={{
                src: "/qi-plus-logo.png",
                x: undefined,
                y: undefined,
                height: 24,
                width: 24,
                excavate: true,
              }}
            />
          </div>

          <div className="text-left mt-4 space-y-2">
            <h3 className="text-sm font-semibold">Como funciona?</h3>
            <ul className="text-sm text-gray-700 dark:text-zinc-50 list-decimal list-inside">
              <li>Abra o app do seu banco</li>
              <li>Escolha pagar via Pix</li>
              <li>Copie e cole o código do pagamento ou escaneie o QR Code</li>
            </ul>
          </div>

          <button
            // className="px-4 py-2 text-sm transition"
            className="bg-[#EA1C78] text-white px-3 py-1 rounded disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={() => handleCopyQRCode(pix.qrCode)}
          >
            COPIAR CÓDIGO QR CODE 📋
          </button>

          <div className="text-xs text-gray-500 mt-4 border-t pt-2">
            <p>
              <strong>Identificador</strong>
              <br />
              {pix.identifier}
            </p>
            <p className="mt-1">
              <strong>Prazo de Pagamento</strong>
              <br />
              {formatDate(pix.expiresAt, "dd/MM/yyyy HH:mm", {
                locale: ptBR,
              })}
            </p>
            <p className="mt-2 text-[10px]">
              Pagamento 100% seguro via: Pagarme
            </p>
          </div>
        </Card>
      </DialogContent>
    </Dialog>
  );
};

export default PixPayments;
