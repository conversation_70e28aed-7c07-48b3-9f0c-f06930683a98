{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always", "source.organizeImports": "always", "source.fixAll.ts": "always", "source.sortImports": "always"}, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[yaml]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[dotenv]": {"editor.defaultFormatter": "foxundermoon.shell-format"}}