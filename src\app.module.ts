import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './modules/auth/auth.module';
import { CoreModule } from './modules/core/core.module';
import { CoreServicesModule } from './modules/core/core-services.module';
import { FirebaseModule } from './modules/firebase/firebase.module';
import { MailgunService } from './modules/mailgun/mailgun.service';
import openPixConfig from './modules/openpix/openpix.config';
import { PaymentModule } from './modules/payment/payment.module';
import { PlanModule } from './modules/plan/plan.module';
import { QiUsersModule } from './modules/qiuser/qiuser.module';
import { SubscriptionModule } from './modules/subscription/subscription.module';
import { UpgradeModule } from './modules/upgrade/upgrade.module';
import { WebhookModule } from './modules/webhook/webhook.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [openPixConfig],
    }),
    CoreModule,
    CoreServicesModule,
    FirebaseModule,
    AuthModule,
    QiUsersModule,
    PlanModule,
    PaymentModule,
    WebhookModule,
    UpgradeModule,
    SubscriptionModule.forRoot({
      isGlobal: true,
    }),
  ],
  controllers: [AppController],
  providers: [AppService, MailgunService, ConfigService],
})
export class AppModule { }
