// utils/__tests__/DateUtils.test.ts
import { BadRequestException } from '@nestjs/common';
import { FieldValue, Timestamp } from 'firebase-admin/firestore';
import * as DateUtils from './date.utils';

// Mock do Firebase Admin
jest.mock('firebase-admin/firestore', () => ({
  FieldValue: {
    serverTimestamp: jest.fn(() => 'SERVER_TIMESTAMP_MOCK'),
  },
  Timestamp: {
    now: jest.fn(() => ({
      toMillis: () => 1640995200000, // 2022-01-01 00:00:00 UTC
    })),
    fromDate: jest.fn((date: Date) => ({
      toMillis: () => date.getTime(),
    })),
    fromMillis: jest.fn((millis: number) => ({
      toMillis: () => millis,
    })),
  },
}));

describe('DateUtils', () => {
  // Como você usa Jest.fn() no React para mockar funções de componentes
  const mockDate = new Date('2024-01-15T10:30:00.000Z');
  const mockTimestamp = mockDate.getTime(); // 1705314600000

  beforeEach(() => {
    // Reset dos mocks antes de cada teste, similar ao cleanup de hooks
    jest.clearAllMocks();
    // Mock do Date.now() - similar a como mockamos props em testes React
    jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);
  });

  afterEach(() => {
    // Restore dos mocks, como você faria com useEffect cleanup
    jest.restoreAllMocks();
  });

  describe('currentTimestamp', () => {
    it('deve retornar o timestamp atual', () => {
      const result = DateUtils.currentTimestamp();
      expect(result).toBe(mockTimestamp);
      expect(Date.now).toHaveBeenCalledTimes(1);
    });
  });

  describe('nowUTCDate', () => {
    it('deve retornar a data atual quando não há override', () => {
      const result = DateUtils.nowUTCDate();
      expect(result).toEqual(mockDate);
    });

    it('deve retornar a data override quando fornecida', () => {
      const overrideDate = new Date('2023-12-25T15:45:00.000Z');
      const result = DateUtils.nowUTCDate(overrideDate);
      expect(result).toEqual(overrideDate);
    });
  });

  describe('addDays', () => {
    it('deve adicionar dias corretamente', () => {
      // Como testar um reducer: estado inicial + ação = novo estado
      const timestamp = mockTimestamp;
      const daysToAdd = 5;
      const expected = timestamp + (5 * 24 * 60 * 60 * 1000);

      const result = DateUtils.addDays(timestamp, daysToAdd);
      expect(result).toBe(expected);
    });

    it('deve subtrair dias quando valor negativo', () => {
      const timestamp = mockTimestamp;
      const daysToSubtract = -3;
      const expected = timestamp + (-3 * 24 * 60 * 60 * 1000);

      const result = DateUtils.addDays(timestamp, daysToSubtract);
      expect(result).toBe(expected);
    });
  });

  describe('addMonths', () => {
    it('deve adicionar meses corretamente', () => {
      // Janeiro -> Março (2 meses)
      const januaryTimestamp = new Date('2024-01-15T10:30:00.000Z').getTime();
      const result = DateUtils.addMonths(januaryTimestamp, 2);
      const resultDate = new Date(result);

      expect(resultDate.getMonth()).toBe(2); // Março (0-indexed)
      expect(resultDate.getDate()).toBe(15);
      expect(resultDate.getFullYear()).toBe(2024);
    });

    it('deve lidar com mudança de ano', () => {
      // Dezembro + 2 meses = Fevereiro do próximo ano
      const decemberTimestamp = new Date('2024-12-15T10:30:00.000Z').getTime();
      const result = DateUtils.addMonths(decemberTimestamp, 2);
      const resultDate = new Date(result);

      expect(resultDate.getMonth()).toBe(1); // Fevereiro
      expect(resultDate.getFullYear()).toBe(2025);
    });
  });

  describe('addYears', () => {
    it('deve adicionar anos corretamente', () => {
      const timestamp = new Date('2024-01-15T10:30:00.000Z').getTime();
      const result = DateUtils.addYears(timestamp, 3);
      const resultDate = new Date(result);

      expect(resultDate.getFullYear()).toBe(2027);
      expect(resultDate.getMonth()).toBe(0); // Janeiro
      expect(resultDate.getDate()).toBe(15);
    });

    it('deve lidar com anos bissextos', () => {
      // 29 de fevereiro 2024 (ano bissexto) + 1 ano
      const leapYearTimestamp = new Date('2024-02-29T10:30:00.000Z').getTime();
      const result = DateUtils.addYears(leapYearTimestamp, 1);
      const resultDate = new Date(result);

      // 2025 não é bissexto, então deve ir para 28 de fevereiro
      expect(resultDate.getFullYear()).toBe(2025);
      expect(resultDate.getMonth()).toBe(2); // Março
      expect(resultDate.getDate()).toBe(1);
    });
  });

  describe('Firebase Timestamp Functions', () => {
    describe('serverTimestamp', () => {
      it('deve retornar FieldValue.serverTimestamp', () => {
        const result = DateUtils.serverTimestamp();
        expect(result).toBe(FieldValue.serverTimestamp);
      });
    });

    describe('firebaseTimestamp', () => {
      it('deve retornar Timestamp.now()', () => {
        const result = DateUtils.firebaseTimestamp();
        expect(Timestamp.now).toHaveBeenCalledTimes(1);
        expect(result).toEqual({ toMillis: expect.any(Function) });
      });
    });

    describe('fromFirebaseTimestamp', () => {
      it('deve converter Timestamp para number', () => {
        const mockFirebaseTimestamp = {
          toMillis: jest.fn(() => mockTimestamp),
        } as any;

        const result = DateUtils.fromFirebaseTimestamp(mockFirebaseTimestamp);
        expect(result).toBe(mockTimestamp);
        expect(mockFirebaseTimestamp.toMillis).toHaveBeenCalledTimes(1);
      });
    });

    describe('toFirebaseTimestamp', () => {
      it('deve converter number para Timestamp', () => {
        const result = DateUtils.toFirebaseTimestamp(mockTimestamp);
        expect(Timestamp.fromMillis).toHaveBeenCalledWith(mockTimestamp);
      });

      it('deve converter Date para Timestamp', () => {
        const result = DateUtils.toFirebaseTimestamp(mockDate);
        expect(Timestamp.fromDate).toHaveBeenCalledWith(mockDate);
      });

      it('deve converter string para Timestamp', () => {
        const dateString = '2024-01-15T10:30:00.000Z';
        const result = DateUtils.toFirebaseTimestamp(dateString);
        expect(Timestamp.fromDate).toHaveBeenCalledWith(new Date(dateString));
      });
    });
  });

  describe('validateClientTimestamp', () => {
    it('deve validar timestamp dentro do limite', () => {
      // Timestamp 1 hora no passado
      const oneHourAgo = mockTimestamp - (1 * 60 * 60 * 1000);

      expect(() => {
        DateUtils.validateClientTimestamp(oneHourAgo);
      }).not.toThrow();
    });

    it('deve lançar BadRequestException para timestamp muito antigo', () => {
      // Timestamp 25 horas no passado (limite padrão é 24h)
      const tooOld = mockTimestamp - (25 * 60 * 60 * 1000);

      expect(() => {
        DateUtils.validateClientTimestamp(tooOld);
      }).toThrow(BadRequestException);
    });

    it('deve lançar BadRequestException para timestamp muito futuro', () => {
      // Timestamp 25 horas no futuro
      const tooFuture = mockTimestamp + (25 * 60 * 60 * 1000);

      expect(() => {
        DateUtils.validateClientTimestamp(tooFuture);
      }).toThrow(BadRequestException);
    });

    it('deve aceitar limite customizado', () => {
      // Timestamp 2 horas no passado com limite de 1 hora
      const twoHoursAgo = mockTimestamp - (2 * 60 * 60 * 1000);

      expect(() => {
        DateUtils.validateClientTimestamp(twoHoursAgo, 1);
      }).toThrow(BadRequestException);
    });

    it('deve incluir informações detalhadas na exceção', () => {
      const tooOld = mockTimestamp - (25 * 60 * 60 * 1000);

      try {
        DateUtils.validateClientTimestamp(tooOld);
      } catch (error: any) {
        expect(error).toBeInstanceOf(BadRequestException);
        expect(error.message).toContain('Timestamp inválido');
        expect(error.message).toContain('24h');
        expect(error.message).toContain('25h');
      }
    });
  });

  describe('isValidClientTimestamp', () => {
    it('deve retornar válido para timestamp correto', () => {
      const oneHourAgo = mockTimestamp - (1 * 60 * 60 * 1000);

      const result = DateUtils.isValidClientTimestamp(oneHourAgo);

      expect(result.isValid).toBe(true);
      expect(result.diffHours).toBe(1);
      expect(result.message).toBeUndefined();
    });

    it('deve retornar inválido com detalhes para timestamp incorreto', () => {
      const tooOld = mockTimestamp - (25 * 60 * 60 * 1000);

      const result = DateUtils.isValidClientTimestamp(tooOld);

      expect(result.isValid).toBe(false);
      expect(result.diffHours).toBe(25);
      expect(result.message).toContain('25h excede o limite de 24h');
    });
  });

  describe('Difference Calculations', () => {
    const timestamp1 = mockTimestamp;
    const timestamp2 = mockTimestamp + (2 * 60 * 60 * 1000); // 2 horas depois

    describe('diff', () => {
      it('deve calcular diferença absoluta em milissegundos', () => {
        const result = DateUtils.diff(timestamp1, timestamp2);
        expect(result).toBe(2 * 60 * 60 * 1000);
      });

      it('deve retornar valor absoluto independente da ordem', () => {
        const result1 = DateUtils.diff(timestamp1, timestamp2);
        const result2 = DateUtils.diff(timestamp2, timestamp1);
        expect(result1).toBe(result2);
      });
    });

    describe('diffInHours', () => {
      it('deve calcular diferença em horas', () => {
        const result = DateUtils.diffInHours(timestamp1, timestamp2);
        expect(result).toBe(2);
      });
    });

    describe('diffInDays', () => {
      it('deve calcular diferença em dias', () => {
        const twoDaysLater = timestamp1 + (2 * 24 * 60 * 60 * 1000);
        const result = DateUtils.diffInDays(timestamp1, twoDaysLater);
        expect(result).toBe(2);
      });
    });
  });

  describe('Age Comparison Functions', () => {
    describe('isOlderThan', () => {
      it('deve retornar true para timestamp mais antigo que o limite', () => {
        const threeHoursAgo = mockTimestamp - (3 * 60 * 60 * 1000);
        // Mockando Date.now para simular "agora"
        jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);

        const result = DateUtils.isOlderThan(threeHoursAgo, 2);
        expect(result).toBe(true);
      });

      it('deve retornar false para timestamp mais recente que o limite', () => {
        const oneHourAgo = mockTimestamp - (1 * 60 * 60 * 1000);
        jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);

        const result = DateUtils.isOlderThan(oneHourAgo, 2);
        expect(result).toBe(false);
      });
    });

    describe('isNewerThan', () => {
      it('deve ser o oposto de isOlderThan', () => {
        const threeHoursAgo = mockTimestamp - (3 * 60 * 60 * 1000);
        jest.spyOn(Date, 'now').mockReturnValue(mockTimestamp);

        const older = DateUtils.isOlderThan(threeHoursAgo, 2);
        const newer = DateUtils.isNewerThan(threeHoursAgo, 2);

        expect(older).toBe(!newer);
      });
    });
  });

  describe('UTC Day Functions', () => {
    const testTimestamp = new Date('2024-01-15T14:30:45.123Z').getTime();

    describe('startOfDayUTC', () => {
      it('deve retornar início do dia em UTC', () => {
        const result = DateUtils.startOfDayUTC(testTimestamp);
        const resultDate = new Date(result);

        expect(resultDate.getUTCHours()).toBe(0);
        expect(resultDate.getUTCMinutes()).toBe(0);
        expect(resultDate.getUTCSeconds()).toBe(0);
        expect(resultDate.getUTCMilliseconds()).toBe(0);
        expect(resultDate.getUTCDate()).toBe(15);
      });
    });

    describe('endOfDayUTC', () => {
      it('deve retornar fim do dia em UTC', () => {
        const result = DateUtils.endOfDayUTC(testTimestamp);
        const resultDate = new Date(result);

        expect(resultDate.getUTCHours()).toBe(23);
        expect(resultDate.getUTCMinutes()).toBe(59);
        expect(resultDate.getUTCSeconds()).toBe(59);
        expect(resultDate.getUTCMilliseconds()).toBe(999);
        expect(resultDate.getUTCDate()).toBe(15);
      });
    });
  });

  describe('createUTC', () => {
    it('deve criar timestamp UTC correto com parâmetros completos', () => {
      const result = DateUtils.createUTC(2024, 3, 15, 14, 30);
      const resultDate = new Date(result);

      expect(resultDate.getUTCFullYear()).toBe(2024);
      expect(resultDate.getUTCMonth()).toBe(2); // Março (0-indexed)
      expect(resultDate.getUTCDate()).toBe(15);
      expect(resultDate.getUTCHours()).toBe(14);
      expect(resultDate.getUTCMinutes()).toBe(30);
    });

    it('deve usar valores padrão para hora e minuto', () => {
      const result = DateUtils.createUTC(2024, 3, 15);
      const resultDate = new Date(result);

      expect(resultDate.getUTCHours()).toBe(0);
      expect(resultDate.getUTCMinutes()).toBe(0);
    });
  });

  describe('Date Getters', () => {
    const testTimestamp = new Date('2024-03-15T14:30:45.123Z').getTime();

    describe('getDate', () => {
      it('deve retornar dia do mês', () => {
        const result = DateUtils.getDate(testTimestamp);
        expect(result).toBe(15);
      });
    });

    describe('getMonth', () => {
      it('deve retornar mês (0-indexed)', () => {
        const result = DateUtils.getMonth(testTimestamp);
        expect(result).toBe(2); // Março = 2
      });
    });

    describe('getFullYear', () => {
      it('deve retornar ano completo', () => {
        const result = DateUtils.getFullYear(testTimestamp);
        expect(result).toBe(2024);
      });
    });
  });

  describe('Formatting Functions', () => {
    const testTimestamp = new Date('2024-03-15T14:30:45.123Z').getTime();

    describe('formatForLog', () => {
      it('deve retornar string ISO', () => {
        const result = DateUtils.formatForLog(testTimestamp);
        expect(result).toBe('2024-03-15T14:30:45.123Z');
      });
    });

    describe('formatForEmail', () => {
      it('deve formatar com hora por padrão', () => {
        const result = DateUtils.formatForEmail(testTimestamp);
        expect(result).toBe('15 de março de 2024 às 14:30');
      });

      it('deve formatar sem hora quando solicitado', () => {
        const result = DateUtils.formatForEmail(testTimestamp, false);
        expect(result).toBe('15 de março de 2024');
      });

      it('deve formatar outros meses corretamente', () => {
        const januaryTimestamp = new Date('2024-01-05T09:15:00.000Z').getTime();
        const result = DateUtils.formatForEmail(januaryTimestamp);
        expect(result).toBe('5 de janeiro de 2024 às 09:15');
      });
    });

    describe('formatForEmailCompact', () => {
      it('deve retornar formato compacto', () => {
        const result = DateUtils.formatForEmailCompact(testTimestamp);
        expect(result).toBe('15/03/2024 14:30');
      });

      it('deve fazer padding de zeros corretamente', () => {
        const earlyTimestamp = new Date('2024-01-05T09:05:00.000Z').getTime();
        const result = DateUtils.formatForEmailCompact(earlyTimestamp);
        expect(result).toBe('05/01/2024 09:05');
      });
    });

    describe('formatPeriodForEmail', () => {
      it('deve formatar período no mesmo dia', () => {
        const startTimestamp = new Date('2024-03-15T14:30:00.000Z').getTime();
        const endTimestamp = new Date('2024-03-15T16:45:00.000Z').getTime();

        const result = DateUtils.formatPeriodForEmail(startTimestamp, endTimestamp);
        expect(result).toBe('15 de março de 2024 das 14:30 às 16:45');
      });

      it('deve formatar período em dias diferentes', () => {
        const startTimestamp = new Date('2024-03-15T22:30:00.000Z').getTime();
        const endTimestamp = new Date('2024-03-16T02:15:00.000Z').getTime();

        const result = DateUtils.formatPeriodForEmail(startTimestamp, endTimestamp);
        expect(result).toBe('15 de março de 2024 às 22:30 até 16 de março de 2024 às 02:15');
      });

      it('deve formatar período em meses diferentes', () => {
        const startTimestamp = new Date('2024-03-31T23:00:00.000Z').getTime();
        const endTimestamp = new Date('2024-04-01T01:30:00.000Z').getTime();

        const result = DateUtils.formatPeriodForEmail(startTimestamp, endTimestamp);
        expect(result).toBe('31 de março de 2024 às 23:00 até 1 de abril de 2024 às 01:30');
      });
    });
  });

  describe('Edge Cases & Error Handling', () => {
    it('deve lidar com timestamps zerados', () => {
      const zeroTimestamp = 0;
      expect(() => DateUtils.formatForLog(zeroTimestamp)).not.toThrow();
      expect(DateUtils.formatForLog(zeroTimestamp)).toBe('1970-01-01T00:00:00.000Z');
    });

    it('deve lidar com timestamps negativos', () => {
      const negativeTimestamp = -86400000; // 1 dia antes do epoch
      expect(() => DateUtils.formatForLog(negativeTimestamp)).not.toThrow();
    });

    it('deve lidar com timestamps muito grandes', () => {
      const largeTimestamp = 8640000000000000; // Limite máximo do JavaScript Date
      expect(() => DateUtils.formatForLog(largeTimestamp)).not.toThrow();
    });
  });

  // describe('Integration Test Scenarios', () => {
  //   // Testes que simulam cenários reais de uso, como você faria para testar fluxos completos
  //   it('deve criar, validar e formatar evento completo', () => {
  //     const eventTimestamp = mockTimestamp + (7 * 24 * 60 * 60 * 1000); // 7 dias no futuro

  //     // Validar timestamp do cliente
  //     expect(() => DateUtils.validateClientTimestamp(eventTimestamp)).not.toThrow();

  //     // Converter para Firebase
  //     const firebaseTimestamp = DateUtils.toFirebaseTimestamp(eventTimestamp);
  //     expect(firebaseTimestamp).toBeDefined();

  //     // Formatar para email
  //     const emailFormat = DateUtils.formatForEmail(eventTimestamp);
  //     expect(emailFormat).toContain('de');
  //     expect(emailFormat).toContain('2024');
  //   });

  //   it('deve calcular período de reunião e formatar corretamente', () => {
  //     const meetingStart = mockTimestamp + (2 * 60 * 60 * 1000); // 2 horas no futuro
  //     const meetingEnd = DateUtils.addDays(meetingStart, 0) + (90 * 60 * 1000); // 1.5 horas depois

  //     // Verificar duração
  //     const durationHours = DateUtils.diffInHours(meetingStart, meetingEnd);
  //     expect(durationHours).toBe(1.5);

  //     // Formatar período
  //     const periodFormat = DateUtils.formatPeriodForEmail(meetingStart, meetingEnd);
  //     expect(periodFormat).toContain('das');
  //     expect(periodFormat).toContain('às');
  //   });
  // });
});