import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AddressDto } from './address.dto';

export class CustomerDto {
  @IsOptional()
  @IsString()
  id?: string;

  @IsString()
  name: string;

  @IsEmail({}, { message: 'Email inválido' })
  email: string;

  @IsString()
  code: string;

  @IsString()
  document: string;

  @IsEnum(['cpf', 'cnpj'], { message: 'O documento deve ser CPF ou CNPJ' })
  document_type: 'cpf' | 'cnpj';

  @IsEnum(['individual', 'company'], {
    message: 'O tipo deve ser individual ou company',
  })
  type: 'individual' | 'company';

  @IsOptional()
  @IsEnum(['male', 'female', 'other'], { message: 'Gênero inválido' })
  gender?: 'male' | 'female' | 'other';

  @IsOptional()
  @IsBoolean()
  delinquent?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;

  @IsOptional()
  created_at?: string;

  @IsOptional()
  updated_at?: string;

  @IsOptional()
  birthdate?: string;

  @IsOptional()
  phones?: Record<string, unknown>; // Ajuste conforme necessário
}
