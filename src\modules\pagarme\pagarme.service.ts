import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { Gateway } from 'src/modules/payment/entities/gateway.interface';
import Logger from 'src/utils/logger';
import { CustomerDto } from './dto/customer.dto';
import { PagarmeGatewayType } from './dto/pagarme.gateway.dto';
import { PaginationDto } from './dto/pagination.dto';
import { OrderRequest, OrderResponse } from './types/pagarme.order.type';
import { PagarmeSplit } from './types/pagarme.split.type';
import {
  CardsResponse,
  CreateCard,
  Customer,
  InvoicesResponse,
  PagarmeCardResponse,
  PagarmeCustomerResponse,
  SubscriptionIncrementsResponse,
  SubscriptionItemsResponse,
  SubscriptionRequest,
  SubscriptionResponse,
  SubscriptionsResponse,
} from './types/pagarme.type';

interface PagarmeError {
  response: {
    data: {
      message: string;
      errors: any[];
      request: any;
    };
  };
}

interface InvoiceResponseData {
  charge: {
    last_transaction: {
      id: string;
      transaction_type: string;
      gateway_id: string;
      amount: number;
      status: string;
      success: boolean;
      url: string;
      pdf: string;
      line: string;
      barcode: string;
      qr_code: string;
      nosso_numero: string;
      bank: string;
      document_number: string;
      instructions: string;
      due_at: string;
      created_at: string;
      updated_at: string;
      gateway_response: {
        code: string;
      };
      antifraud_response: {};
      metadata: {};
    };
  };
}

@Injectable()
export class PagarmeService implements Gateway<PagarmeGatewayType> {
  private readonly pagarmeInstance: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get('PAGARME_SECRET_KEY');
    if (!apiKey) {
      throw new Error('PAGARME_SECRET_KEY is missing');
    }
    this.pagarmeInstance = axios.create({
      baseURL:
        this.configService.get('PAGARME_API_URL') ||
        'https://api.pagar.me/core/v5',
      headers: {
        'X-Custom-Header': 'foobar',
        Authorization: `Basic ${btoa(apiKey + ':')}`,
      },
    });
  }

  async findCustomerDocument(document: string): Promise<any> {
    throw new Error('Method not implemented.');
  }

  async listCostumers(
    pagination?: PaginationDto,
  ): Promise<PagarmeCustomerResponse | string> {
    try {
      const response = await this.pagarmeInstance.get('/customers', {
        params: {
          page: pagination?.page || 1,
          size: pagination?.size || 10,
        },
      });
      return response.data as PagarmeCustomerResponse;
    } catch (error) {
      if (error instanceof Error) {
        return error.message;
      }
      return 'Ocorreu um erro desconhecido';
    }
  }
  async getCustomer(document: string): Promise<Customer | string> {
    try {
      const { data } = await this.pagarmeInstance.get(`/customers`, {
        params: {
          document,
        },
      });
      if (data.length > 0) {
        return data.data[0];
      }
      return 'Cliente não encontrado';
    } catch (error) {
      if (error instanceof Error) {
        return error.message;
      }
      return 'Ocorreu um erro desconhecido';
    }
  }

  async createCustomer(
    customerData: CustomerDto,
  ): Promise<PagarmeCustomerResponse | object> {
    try {
      const adaptedCustomer = {
        ...customerData,
        address: {
          ...customerData.address,
          zip_code: customerData.address?.postalCode,
        },
      };
      const response = await this.pagarmeInstance.post(
        '/customers',
        adaptedCustomer,
      );
      return response.data as PagarmeCustomerResponse;
    } catch (error: any) {
      if (error instanceof Error && 'response' in error) {
        const pagarmeError = error as PagarmeError;
        const data = {
          message: pagarmeError.response.data.message,
          errors: pagarmeError.response.data.errors,
          error: true,
        };
        return data;
      }
      console.error(error);
      return {
        message: 'An unknown error occurred',
        errors: [],
        error: true,
      };
    }
  }

  async updateCustomer(
    customerId: string,
    customerData: CustomerDto & { metadata?: Record<string, unknown> },
  ): Promise<PagarmeCustomerResponse | object> {
    try {
      const adaptedCustomer = {
        ...customerData,
        address: {
          ...customerData.address,
          zip_code: customerData.address?.postalCode,
        },
      };
      const response = await this.pagarmeInstance.put(
        `/customers/${customerId}`,
        adaptedCustomer,
      );
      return response.data as PagarmeCustomerResponse;
    } catch (error: any) {
      if (error instanceof Error && 'response' in error) {
        const pagarmeError = error as PagarmeError;
        const data = {
          message: pagarmeError.response.data.message,
          errors: pagarmeError.response.data.errors,
          error: true,
        };
        return data;
      }
      console.error(error);
      return {
        message: 'An unknown error occurred',
        errors: [],
        error: true,
      };
    }
  }

  async findCustomerByDocument(
    document: string,
  ): Promise<PagarmeCustomerResponse | string> {
    try {
      const response = await this.pagarmeInstance.get(`/customers`, {
        params: {
          document,
        },
      });
      return response.data as PagarmeCustomerResponse;
    } catch (error) {
      return 'Ocorreu um erro desconhecido';
    }
  }

  async findCustomerByUid(uid: string): Promise<Customer | null> {
    try {
      const { data } = await this.pagarmeInstance.get(`/customers`, {
        params: {
          code: uid,
        },
      });
      if (data.data.length > 0) {
        return data.data[0];
      }
      return null;
    } catch (error) {
      console.error(error);
      return null;
    }
  }

  async findCustomerIdByUid(uid: string): Promise<string | null> {
    const customer = await this.findCustomerByUid(uid);
    return customer?.id || null;
  }

  async createCard(
    cardData: CreateCard,
  ): Promise<PagarmeCardResponse | object | string> {
    const customerId = cardData.customer_id;
    if (!customerId) {
      return 'O ID do cliente é obrigatório';
    }
    const adaptedCard = {
      ...cardData,
      billing_address: {
        ...cardData.billing_address,
        zip_code: cardData.billing_address?.postalCode,
      },
    };
    try {
      const response = await this.pagarmeInstance.post(
        `/customers/${customerId}/cards`,
        adaptedCard,
      );
      return response.data as PagarmeCardResponse;
    } catch (error) {
      const data = {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
      };

      return data;
    }
  }

  async listCards(
    customer_id: string,
    pagination?: PaginationDto,
  ): Promise<CardsResponse> {
    try {
      const response = await this.pagarmeInstance.get(
        `/customers/${customer_id}/cards`,
        {
          params: {
            page: pagination?.page || 1,
            size: pagination?.size || 10,
          },
        },
      );
      return {
        error: false,
        errors: [],
        message: 'Cards found',
        data: response.data.data,
        paging: response.data.paging,
      };
    } catch (error) {
      return {
        message:
          (error as PagarmeError).response?.data?.message ||
          'Ocorreu um erro desconhecido',
        errors: (error as PagarmeError).response?.data?.errors || [],
        error: true,
        data: [],
        paging: { total: 0 },
      };
    }
  }

  async getCard(
    customerId: string,
    cardId: string,
  ): Promise<PagarmeCardResponse | object | string> {
    try {
      const response = await this.pagarmeInstance.get(
        `/customers/${customerId}/cards/${cardId}`,
      );
      return response.data as PagarmeCardResponse;
    } catch (error) {
      return {
        error: true,
        message:
          (error as PagarmeError).response?.data?.message ||
          'Ocorreu um erro desconhecido',
        errors: (error as PagarmeError).response?.data?.errors || [],
      };
    }
  }

  async deleteCard(
    customer_id: string,
    card_id: string,
  ): Promise<{ error: boolean; message: string; errors: any[] }> {
    try {
      await this.pagarmeInstance.delete(
        `/customers/${customer_id}/cards/${card_id}`,
      );
      return {
        error: false,
        message: 'Card deleted successfully',
        errors: [],
      };
    } catch (error) {
      return {
        error: true,
        message:
          (error as PagarmeError).response?.data?.message ||
          'Ocorreu um erro desconhecido',
        errors: (error as PagarmeError).response?.data?.errors || [],
      };
    }
  }

  async createSubscription(
    subscriptionData: SubscriptionRequest,
  ): Promise<SubscriptionResponse> {
    try {
      const response = await this.pagarmeInstance.post(
        '/subscriptions',
        subscriptionData,
      );
      const responseData = response.data as SubscriptionResponse['data'];
      return {
        error: false,
        errors: [],
        message: 'Subscription created',
        data: responseData,
      };
    } catch (error) {
      const data = {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as SubscriptionResponse['data'],
      };
      Logger(data, 'ERROR CREATE SUBSCRIPTION');

      return data;
    }
  }

  async listSubscriptions(
    customer_id: string,
    pagination?: PaginationDto,
  ): Promise<SubscriptionsResponse> {
    try {
      const response = await this.pagarmeInstance.get(`/subscriptions`, {
        params: {
          customer_id,
          page: pagination?.page || 1,
          size: pagination?.size || 10,
        },
      });
      const responseData = response.data as SubscriptionsResponse['data'];
      return {
        error: false,
        errors: [],
        message: 'subscriptions found',
        data: responseData['data'],
      };
    } catch (error) {
      const data = {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as SubscriptionsResponse['data'],
      };

      return data;
    }
  }

  async getSubscription(
    subscription_id: string,
  ): Promise<SubscriptionResponse> {
    return this.pagarmeInstance.get(`/subscriptions/${subscription_id}`);
  }

  async cancelSubscription(
    subscription_id: string,
  ): Promise<SubscriptionResponse> {
    try {
      const response = await this.pagarmeInstance.delete(
        `/subscriptions/${subscription_id}`,
      );
      const responseData = response.data as SubscriptionResponse['data'];
      return {
        error: false,
        errors: [],
        message: 'Subscription canceled',
        data: responseData['data'],
      };
    } catch (error) {
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as SubscriptionResponse['data'],
      };
    }
  }

  async enableManualBilling(
    subscription_id: string,
  ): Promise<SubscriptionResponse> {
    try {
      const response = await this.pagarmeInstance.post(
        `/subscriptions/${subscription_id}/manual-billing`,
      );
      const responseData = response.data as SubscriptionResponse['data'];
      let metadata = {};
      if (responseData.metadata) {
        metadata = {
          ...responseData.metadata,
          manualBilling: true,
        };
      }
      metadata['canceledAt'] = new Date().toISOString();
      responseData.metadata = metadata;

      const updatedSubscription = await this.updateSubscriptionMetadata(
        subscription_id,
        metadata,
      );

      if (updatedSubscription.error) {
        await this.disableManualBilling(subscription_id);
        return {
          error: true,
          errors: updatedSubscription.errors,
          message: updatedSubscription.message,
          data: responseData,
        };
      }

      return {
        error: false,
        errors: [],
        message: 'Manual billing enabled',
        data: responseData,
      };
    } catch (error) {
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as SubscriptionResponse['data'],
      };
    }
  }

  async disableManualBilling(
    subscription_id: string,
  ): Promise<SubscriptionResponse> {
    try {
      const response = await this.pagarmeInstance.delete(
        `/subscriptions/${subscription_id}/manual-billing`,
      );
      const responseData = response.data as SubscriptionResponse['data'];
      let metadata = {};
      if (responseData.metadata) {
        metadata = {
          ...responseData.metadata,
          manualBilling: false,
        };
      }
      delete metadata['canceledAt'];
      responseData.metadata = metadata;

      const updatedSubscription = await this.updateSubscriptionMetadata(
        subscription_id,
        metadata,
      );

      if (updatedSubscription.error) {
        await this.enableManualBilling(subscription_id);
        return {
          error: true,
          errors: updatedSubscription.errors,
          message: updatedSubscription.message,
          data: responseData,
        };
      }

      return {
        error: false,
        errors: [],
        message: 'Manual billing disabled',
        data: responseData,
      };
    } catch (error) {
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as SubscriptionResponse['data'],
      };
    }
  }

  async listInvoices(
    subscription_id: string,
    pagination?: PaginationDto,
  ): Promise<InvoicesResponse> {
    try {
      const response = await this.pagarmeInstance.get(`/invoices`, {
        params: {
          subscription_id,
          page: pagination?.page || 1,
          size: pagination?.size || 10,
        },
      });

      return response.data as InvoicesResponse;
    } catch (error) {
      if (error instanceof Error) {
        const data: InvoicesResponse = {
          message: error.message,
          errors: [],
          error: true,
          data: [],
          paging: { total: 0 },
        };
        return data;
      }
      const data: InvoicesResponse = {
        message: 'Ocorreu um erro desconhecido',
        errors: [],
        error: true,
        data: [],
        paging: {
          total: 0,
        },
      };
      return data;
    }
  }

  async getInvoice(invoice_id: string): Promise<any> {
    try {
      const response = await this.pagarmeInstance.get(
        `/invoices/${invoice_id}`,
      );
      const { url, pdf, qr_code, barcode } = (
        response.data as InvoiceResponseData
      ).charge.last_transaction;
      return {
        url,
        pdf,
        qr_code,
        barcode,
      };
    } catch (error) {
      console.error('ERROR INVOICE', error);
      return 'Ocorreu um erro desconhecido';
    }
  }

  async listRecipients({ page = 1, size = 10 }): Promise<any> {
    try {
      const response = await this.pagarmeInstance.get('/recipients', {
        params: { page, size },
      });
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        return error.response.data.message;
      }

      Logger(error.response.data, 'ERROR RESPONSE');
    }
  }

  async editSplit(subscriptionId: string, split: PagarmeSplit): Promise<any> {
    try {
      const response = await this.pagarmeInstance.patch(
        `/subscriptions/${subscriptionId}/split`,
        split,
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.message) {
        return error.response.data.message;
      }

      if (error instanceof Error) {
        return error.message;
      }

      console.error('ERROR', error);
      return 'Ocorreu um erro desconhecido';
    }
  }

  async getSubscriptionItems(
    subscription_id: string,
    pagination?: PaginationDto,
  ): Promise<SubscriptionItemsResponse> {
    try {
      const response = await this.pagarmeInstance.get(
        `/subscriptions/${subscription_id}/items`,
        {
          params: {
            page: pagination?.page || 1,
            size: pagination?.size || 10,
          },
        },
      );
      return {
        error: false,
        errors: [],
        message: 'Subscription items found',
        data: response.data,
      };
    } catch (error) {
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {
          data: [],
          paging: { total: 0 },
        },
      };
    }
  }

  async getSubscriptionIncrements(
    subscription_id: string,
    pagination?: PaginationDto,
  ): Promise<SubscriptionIncrementsResponse> {
    try {
      const response = await this.pagarmeInstance.get(
        `/subscriptions/${subscription_id}/increments`,
        {
          params: {
            page: pagination?.page || 1,
            size: pagination?.size || 10,
          },
        },
      );
      return {
        error: false,
        errors: [],
        message: 'Subscription increments found',
        data: response.data,
      };
    } catch (error) {
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {
          data: [],
          paging: { total: 0 },
        },
      };
    }
  }

  async createOrder(orderData: OrderRequest): Promise<OrderResponse> {
    try {
      const response = await this.pagarmeInstance.post('/orders', orderData);
      return {
        message: 'Order created',
        errors: [],
        error: false,
        data: response.data,
      };
    } catch (error: unknown) {
      if (error instanceof AxiosError) {
        console.error('ERROR', error?.response?.data);
        return {
          message: (error as PagarmeError).response.data.message,
          errors: (error as PagarmeError).response.data.errors,
          error: true,
          data: {} as OrderResponse['data'],
        };
      }
      return {
        message: 'Ocorreu um erro desconhecido',
        errors: [],
        error: true,
        data: {} as OrderResponse['data'],
      };
    }
  }

  async cancelOrder(orderId: string): Promise<OrderResponse> {
    try {
      const response = await this.pagarmeInstance.patch(
        `/orders/${orderId}/closed`,
        { status: 'canceled' },
      );
      return {
        message: 'Order canceled',
        errors: [],
        error: false,
        data: response.data,
      };
    } catch (error) {
      console.error('ERROR', error);
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as OrderResponse['data'],
      };
    }
  }

  async updateSubscriptionMetadata(
    subscription_id: string,
    metadata: Record<string, unknown>,
  ): Promise<OrderResponse> {
    try {
      const response = await this.pagarmeInstance.patch(
        `/subscriptions/${subscription_id}/metadata`,
        {
          metadata,
        },
      );
      return {
        message: 'Subscription metadata updated',
        errors: [],
        error: false,
        data: response.data,
      };
    } catch (error) {
      console.error('ERROR', error);
      return {
        message: (error as PagarmeError).response.data.message,
        errors: (error as PagarmeError).response.data.errors,
        error: true,
        data: {} as OrderResponse['data'],
      };
    }
  }

  async updateSubscription(
    subscription_id: string,
    subscriptionData: any,
  ): Promise<SubscriptionResponse> {
    throw new Error('Not implemented');
  }
}
