Atividades restantes do checkout:

- _upgrade_ Aplicar o desconto na primeira e se sobrar nas demais parcelas - pendente
- Adicionar uma badge ao planCard para exibir quando houver personalização do plano, algo como:
  '+R$1.000,00 de adicionais personalizados' - feito
- Ajustar os vencimentos das faturas para seguir a mesma regra da data de cobrança - feito
- Ajustar o card do item para exibir a quantidade, a descrição e o valor de forma mais elegante - feito
- Adicionar o numero de parcelas ao histórico de assinaturas - feito
- _upgrade_ Verificar se o valor cobrado no upgrade está correto - em andamento
- Corrigir no webhook para não atualizar o qr_code com os pedidos subsequentes quando o pagamento é parcelado - feito
- Verificar a data no card do plano, está pegando billing date - feito
- Sugerir o próximo plano caso o valor do atual com customização seja maior - pendente
- DEFINIÇÂO:
-     Upgrade com desconto no total
-     Dowgrade financeiro, aplica o upgrade e não cobra até consumir o crédito
- Nos casos em que o usuário pode selecionar a data de cobrança da fatura, devemos calcular o valor da entrada proporcional ao dia que ele está assinando até o dia escolhido por ele no mês seguinte - feito
