import { getDiscount, getDiscountPercentage } from "@/lib/plan.utils";
import {
  CalculatedPlanValue,
  CustomFeature,
  PagarmeData,
  Plan,
  PlanFeature,
  PlanOption,
  UpgradeValue,
} from "@/types/plan-types";

export class PlanModel implements Plan {
  uniqueId: string;
  id: string;
  name: string;
  tag: string;
  monthlyPrice: number;
  yearlyPrice: number;
  description: string;
  img: string | null;
  features: PlanFeature[];
  isPopular?: boolean;
  options: PlanOption[];
  customFeatures: CustomFeature[];
  originalFeatures: CustomFeature[];
  calculatedValues: CalculatedPlanValue;
  planData;
  leadsCount: number;
  pagarme: PagarmeData[];
  credit: number;
  isCurrentPlan?: boolean;
  config: {
    order: number;
  };
  isYearly: boolean;
  discount: number;
  discountPercentage: number;

  constructor(data: Partial<Plan>) {
    this.uniqueId = data.uniqueId || "";
    this.id = data.id || "";
    this.name = data.name || "";
    this.tag = data.tag || "";
    this.description = data.description || "";
    this.img = data.img;
    this.features = data.features || [];
    this.isPopular = data.isPopular;
    this.options = data.options || [];
    this.customFeatures = data.customFeatures || [];
    this.originalFeatures = data.originalFeatures || [];
    this.planData = data.planData || null;
    this.leadsCount = data.leadsCount || 0;
    this.pagarme = data.pagarme || [];
    this.credit = data.credit || 0;
    this.isCurrentPlan = data.isCurrentPlan || false;
    this.config = data.config;
    this.isYearly = data.isYearly;
    this.discount = data.discount || 0;
    this.discountPercentage = data.discountPercentage || 0;
  }

  static parse(data): Plan {
    const options = (data.options || [])
      .filter((option) => option.contacts_min > 0 && option.contacts_max > 0)
      .map((option) => ({
        contacts_min: Number(option.contacts_min),
        contacts_max: Number(option.contacts_max),
        monthly_value: Number(option.monthly_value),
        yearly_value: Number(option.yearly_value),
      }));

    const sanitizedOptions = options.reduce((options, option) => {

      const discount = getDiscount(option.monthly_value, option.yearly_value) * 12;
      const discountPercentage = getDiscountPercentage(option.monthly_value, option.yearly_value);

      // Find existing options with same monthly value
      const existingOption = options.find(
        (existing) => existing.monthly_value === option.monthly_value,
      );

      if (!existingOption) {
        options.push({
          ...option,
          discount,
          discountPercentage,
        });
      } else {
        // Keep only min and max leads for same price
        const sameValueOptions = options.filter(
          (opt) => opt.monthly_value === option.monthly_value,
        );
        const minLeads = Math.min(
          option.contacts_min,
          ...sameValueOptions.map((opt) => opt.contacts_min),
        );
        const maxLeads = Math.max(
          option.contacts_max,
          ...sameValueOptions.map((opt) => opt.contacts_max),
        );

        // Remove all options with same price
        options = options.filter((opt) => opt.monthly_value !== option.monthly_value);

        // Add new option with min and max leads
        options.push({
          ...option,
          contacts_min: minLeads,
          contacts_max: maxLeads,
          discount,
          discountPercentage,
        });
      }
      return options;
    }, []);

    const { discount, discountPercentage, contacts_max } = sanitizedOptions[0];

    const plan = new PlanModel({
      uniqueId: data.uniqueId,
      id: data.id,
      name: data.title,
      tag: "Preferido dos Iniciantes", // Você pode definir uma lógica para determinar a tag
      description: data.data.short_description,
      img: data.image_url,
      features: (data.features || []).map((feature) => ({
        name: feature.title,
        included: true,
        showOnCard: true,
      })),
      options: sanitizedOptions || [],
      planData: data,
      leadsCount: contacts_max || 0,
      pagarme: data.pagarme || [],
      credit: data.credit || 0,
      isCurrentPlan: data.isCurrentPlan || false,
      config: data.config,
      isYearly: data.isYearly,
      discount: discount || 0,
      discountPercentage: discountPercentage || 0,
    });

    const getValue = (key: string, type: string) => {
      key = key.replace("_included", "");
      switch (key) {
        default:
          return data.values[`extra_${key}_${type}`];
      }
    };

    // Possíveis features (se não estiver aqui, não será exibida)
    const features = {
      funnels_included: "Funis de Vendas",
      "landing-pages_included": "Landing Pages",
      qiusers_included: "Usuários",
    };

    Object.entries(data.config).forEach(([key, value]) => {
      if (!key.includes("_included") || Number(value) < 1) return;
      const feature = features[key as keyof typeof features];
      if (!feature) return;
      const monthlyPrice = Number(getValue(key, "monthly"));
      const yearlyPrice = Number(getValue(key, "yearly"));
      if (isNaN(monthlyPrice) || isNaN(yearlyPrice)) return;
      const featureMap = {
        id: key,
        name: feature,
        monthlyPrice,
        yearlyPrice,
        included: Number(value),
        quantity: Number(value),
      };
      plan.customFeatures.push(featureMap);
      plan.originalFeatures.push(featureMap);
    });

    // Adiciona features baseadas nos módulos ativos
    // Object.entries(data.modules).forEach(([module, isActive]) => {
    //   if (isActive) {
    //     plan.features.push({
    //       name: `Módulo ${module.replace(/-/g, ' ').replace(/^\w/, c => c.toUpperCase())}`,
    //       included: true,
    //       showOnCard: false
    //     });
    //   }
    // });

    // Adiciona features baseadas na configuração
    // if (data.config) {
    //   const configFeatures = [
    //     { key: 'users_included', label: 'Usuário(s) incluído(s)' },
    //     { key: 'landing-pages_included', label: 'Páginas de vendas incluídas' },
    //     { key: 'funnels_included', label: 'Funil(is) de vendas incluído(s)' },
    //     { key: 'emails_towards_base', label: 'Disparos de e-mail por lead' }
    //   ];

    //   configFeatures.forEach(({ key, label }) => {
    //     const value = data.config[key as keyof typeof data.config];
    //     if (value && value > 0) {
    //       plan.features.push({
    //         name: label,
    //         included: value || true,
    //         showOnCard: false,
    //       });
    //     }
    //   });
    // }

    return plan;
  }

  static recriate(plan: Plan) {
    return new PlanModel({
      ...plan,
      uniqueId: plan.uniqueId,
      id: plan.id,
      name: plan.name,
      credit: plan.credit,
      isCurrentPlan: plan.isCurrentPlan,
      config: plan.config,
      isYearly: plan.isYearly,
      discount: plan.discount,
      discountPercentage: plan.discountPercentage,
      customFeatures: plan.customFeatures,
      originalFeatures: plan.originalFeatures,
      calculatedValues: plan.calculatedValues,
    });
  }
}
