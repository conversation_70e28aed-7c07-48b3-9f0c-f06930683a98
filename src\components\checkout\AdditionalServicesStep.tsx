import { CountdownTimer } from "@/components/countdown/CountdownTimer";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { calculateDiscountPercentage, formatCurrency } from "@/lib/utils";
import { Additional } from "@/types/additional-types";
import { Rocket, Target, TrendingUp, Users } from "lucide-react";
import { useState } from "react";
import {
  FieldErrors,
  UseFormRegister,
  useF<PERSON><PERSON>ontext,
  useWatch,
} from "react-hook-form";
import { CheckoutFormData } from "./types";
import { useAuth } from "@/contexts/auth/useAuth";
import { Account } from "@/types/account";

interface AdditionalServicesStepProps {
  title: string;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  additionals: Additional[];
  onAdditionalsChange: (additionals: Additional[]) => void;
}

export function AdditionalServicesStep({
  title,
  additionals,
  onAdditionalsChange,
}: AdditionalServicesStepProps) {
  const shotXModule = additionals.find((item) => item.id === "shotx-module");
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const { account = {} as Account, subscription } = useAuth();
  const { control, setValue, watch } = useFormContext();
  const acceptTerms = watch("acceptTerms");
  const handleAcceptTerms = (e) => {
    setValue("acceptTerms", e);
  };
  const handleShotXChange = (_: boolean) => {
    if (!shotXModule.selected) {
      onAdditionalsChange(
        additionals.map((item) => {
          if (item.id === "shotx-module") {
            return { ...item, selected: true };
          }
          return item;
        })
      );
    } else {
      setShowConfirmDialog(true);
    }
  };
  const currentSubscriptionHasShotX = () => {
    if (subscription?.status === "active") {
      return Boolean(subscription?.items.find((item) => item.id === "shotx-module"));
    }
    return false;
  };
  const handleConfirmUnchecked = () => {
    const newAdditionals = additionals.map((item) => {
      if (item.id === "shotx-module") {
        return { ...item, selected: false };
      }
      return item;
    });

    onAdditionalsChange(newAdditionals);
    setShowConfirmDialog(false);
  };

  const handleCancelUnchecked = () => {
    setShowConfirmDialog(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{title}</h3>
        <CountdownTimer />
      </div>

      <div className="space-y-6">
        <div className="flex items-start space-x-3">
          <Checkbox
            id="shotx-module"
            checked={shotXModule.selected}
            onCheckedChange={handleShotXChange}
            className="mt-1"
            disabled={currentSubscriptionHasShotX()}
          />
          <div className="space-y-6 flex-1">
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="shotx-module" className="text-lg font-medium">
                    Módulo ShotX
                  </Label>
                  <div className="p-3 flex items-center gap-1 bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600 px-3 py-1 rounded-full text-white text-xs font-medium shadow-md h-fit">
                    🔥&nbsp;Oferta Especial
                  </div>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Users className="w-4 h-4 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>+500 clientes já utilizam o ShotX</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <div className="text-right">
                  <span className="text-sm text-muted-foreground line-through">
                    {formatCurrency(shotXModule.price)}
                  </span>
                  <span className="ml-2 text-lg font-bold text-green-500">
                    {formatCurrency(
                      shotXModule.price - shotXModule.discountValue
                    )}
                  </span>
                  <Badge variant="outline" className="ml-2 text-green-500">
                    -
                    {calculateDiscountPercentage(
                      shotXModule.price,
                      shotXModule.discountValue
                    ).toFixed(0)}
                    % OFF
                  </Badge>
                </div>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {shotXModule.description}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-start gap-2 p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                <Rocket className="h-5 w-5 text-[#0071e2] shrink-0" />
                <div>
                  <h4 className="font-medium mb-1">Aumente suas Vendas</h4>
                  <p className="text-sm text-muted-foreground">
                    Aumente sua taxa de conversão em até 300%
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2 p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                <Target className="h-5 w-5 text-[#0071e2] shrink-0" />
                <div>
                  <h4 className="font-medium mb-1">Alcance Preciso</h4>
                  <p className="text-sm text-muted-foreground">
                    Segmentação avançada para seu público-alvo
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-2 p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                <TrendingUp className="h-5 w-5 text-[#0071e2] shrink-0" />
                <div>
                  <h4 className="font-medium mb-1">ROI Comprovado</h4>
                  <p className="text-sm text-muted-foreground">
                    Retorno médio de 5x sobre o investimento
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-4 p-4 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 font-medium mb-2">
                <Users className="h-4 w-4" />
                Depoimentos de Clientes
              </div>
              <div className="flex items-start gap-4">
                <Avatar className="w-12 h-12 border-2 border-green-200">
                  <AvatarImage src="" alt="Maria Silva" />
                  <AvatarFallback>MS</AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm italic mb-2">
                    "O ShotX transformou completamente nossa estratégia de
                    vendas. Aumentamos nossa receita em 250% em apenas 3 meses!"
                  </p>
                  <div className="font-medium">Maria Silva</div>
                  <div className="text-sm text-gray-600">CEO da TechPro</div>
                </div>
              </div>
            </div>

            <div className="h-2" />
            <div className="flex items-center space-x-2">
              <Checkbox
                id="terms"
                checked={acceptTerms}
                onCheckedChange={handleAcceptTerms}
              />
              <label
                htmlFor="terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Aceito os
                <a
                  target="_blank"
                  href="https://qiplus.com.br/politicas-de-privacidade-e-uso"
                  className="text-primary underline-offset-4 hover:underline"
                >
                  &nbsp;Termos e Condições de Uso e Politica de Privacidade
                </a>
              </label>
            </div>
          </div>
        </div>
      </div>

      <AlertDialog open={showConfirmDialog}>
        <AlertDialogContent className="max-w-[500px]">
          <AlertDialogHeader>
            <AlertDialogTitle>
              Tem certeza que deseja remover o ShotX?
            </AlertDialogTitle>
            <AlertDialogDescription className="space-y-4">
              <p>
                Você está prestes a remover uma ferramenta poderosa que pode
                transformar seu negócio:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Aumento comprovado de até 300% nas vendas</li>
                <li>Segmentação avançada de público-alvo</li>
                <li>ROI médio de 5x sobre o investimento</li>
                <li>Economia de 53% na oferta especial</li>
              </ul>
              <p className="font-medium text-green-600">
                Não perca esta oportunidade única de potencializar seus
                resultados!
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleConfirmUnchecked}>
              Remover mesmo assim
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelUnchecked}
              className="bg-green-600 hover:bg-green-700"
            >
              Manter o ShotX
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
