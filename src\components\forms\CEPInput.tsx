import { FieldErrors, UseFormRegister } from "react-hook-form";
import InputMask from "react-input-mask";
import { CheckoutFormData } from "../checkout/types";
import { InputMessageError } from "../InputMessageError";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
interface CEPInputProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  toBilling: boolean;
  hasUserData: (field: string) => boolean;
}

export const CEPInput = ({ register, errors, toBilling, hasUserData }: CEPInputProps) => {
  const inputId = toBilling ? "billingPostalCode" : "postalCode";
  const disabled = hasUserData(inputId);

  if (disabled) {
    return (
      <div>
        <Label htmlFor={inputId}>CEP</Label>
        <Input
          id={inputId}
          disabled
          readOnly
          {...register(inputId)}
        />
      </div>
    );
  }

  return (
    <div>
      <Label htmlFor={inputId}>CEP</Label>
      <InputMask
        mask="99999-999"
        maskChar={null}
        id={inputId}
        {...register(inputId, {
          minLength: {
            value: 9,
            message: "CEP inválido",
          },
          required: {
            value: true,
            message: "CEP é obrigatório",
          },
        })}
      >
        {({ inputProps }) => (
          <Input
            // className={`input-class ${errors[inputId] ? 'border-red-500' : ''}`}
            {...inputProps}
            id={inputId}
            {...register(inputId)}
            placeholder="00000-000"
          />
        )}
      </InputMask>
      <InputMessageError error={errors[inputId]?.message} />
    </div>
  );
};
