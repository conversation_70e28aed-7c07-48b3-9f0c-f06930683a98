import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PaginationDto } from 'src/modules/pagarme/dto/pagination.dto';
import {
  PaymentAdapter,
  PaymentAdapterByPaymentMethod,
} from './adapters/adapters';
import { Gateway } from './entities/gateway.interface';
import { PaymentMethod } from './enum/paymentMethod.enum';
import { PaymentGatewayTypes, PaymentProviders } from './types/gateway.types';

@Injectable()
export class GatewayService<T extends PaymentMethod>
  implements Gateway<PaymentGatewayTypes[T]> {
  private readonly provider: Gateway<PaymentGatewayTypes[T]>;
  private readonly adapter: PaymentAdapter;
  private readonly logger = new Logger(GatewayService.name);
  private readonly providerName: string;

  constructor(
    paymentMethod: T,
    private readonly configService: ConfigService,
  ) {
    this.provider = new PaymentProviders[paymentMethod](this.configService);
    this.adapter = new PaymentAdapterByPaymentMethod[paymentMethod]();
    this.providerName = PaymentProviders[paymentMethod].name;
  }
  async listCostumers(pagination?: PaymentGatewayTypes[T]['listCostumersIn']) {
    // this.logger.log('LIST COSTUMERS', pagination, this.providerName);
    return this.provider.listCostumers(pagination);
  }
  async createCustomer(
    customerData: PaymentGatewayTypes[T]['createCustomerIn'],
  ) {
    const customerDataAdapter = this.adapter.toCreateCustomer(customerData);
    // this.logger.log('CREATE CUSTOMER', customerDataAdapter, this.providerName);
    return this.provider.createCustomer(customerDataAdapter);
  }

  async updateCustomer(
    customerId: string,
    customerData: PaymentGatewayTypes[T]['updateCustomerIn'],
  ) {
    const customerDataAdapter = this.adapter.toUpdateCustomer(customerData);
    this.logger.log('UPDATE CUSTOMER', customerDataAdapter, this.providerName);
    return this.provider.updateCustomer(customerId, customerDataAdapter);
  }

  async getCustomer(id: PaymentGatewayTypes[T]['getCustomerIn']) {
    const customerDataAdapter = this.adapter.toGetCustomer(id);
    // this.logger.log('GET CUSTOMER', customerDataAdapter, this.providerName);
    try {
      const response = await this.provider.getCustomer(customerDataAdapter);
      return this.adapter.fromGetCustomer(response);
    } catch (error) {
      this.logger.error('GET CUSTOMER ERROR', error);
      return null;
    }
  }
  async listCards(customerId: string, pagination?: PaginationDto) {
    // this.logger.log('LIST CARDS', customerId, pagination, this.providerName);
    return this.provider.listCards(customerId, pagination);
  }
  async createCard(cardData: PaymentGatewayTypes[T]['createCardIn']) {
    const cardDataAdapter = this.adapter.toCard(cardData);
    // this.logger.log('CREATE CARD', cardDataAdapter, this.providerName);
    return this.provider.createCard(cardDataAdapter);
  }
  async deleteCard(customerId: string, cardId: string) {
    // this.logger.log('DELETE CARD', customerId, cardId, this.providerName);
    return this.provider.deleteCard(customerId, cardId);
  }
  async getCard(customerId: string, cardId: string) {
    return this.provider.getCard(customerId, cardId);
  }
  async listSubscriptions(customerId: string, pagination?: PaginationDto) {
    // this.logger.log(
    //   'LIST SUBSCRIPTIONS',
    //   customerId,
    //   pagination,
    //   this.providerName,
    // );
    return this.provider.listSubscriptions(customerId, pagination);
  }
  async createSubscription(
    subscriptionData: PaymentGatewayTypes[T]['createSubscriptionIn'],
  ) {
    const subscriptionDataAdapter =
      this.adapter.toSubscription(subscriptionData);
    // this.logger.log(
    //   'CREATE SUBSCRIPTION',
    //   subscriptionDataAdapter,
    //   this.providerName,
    // );

    // console.log('qiSubscription', qiSubscription);

    return this.provider.createSubscription(subscriptionDataAdapter);
  }
  async getSubscription(subscriptionId: string) {
    // this.logger.log('GET SUBSCRIPTION', subscriptionId, this.providerName);
    return this.provider.getSubscription(subscriptionId);
  }
  async updateSubscription(subscriptionId: string, subscriptionData: any) {
    // this.logger.log(
    //   'UPDATE SUBSCRIPTION',
    //   subscriptionId,
    //   subscriptionData,
    //   this.providerName,
    // );
    return this.provider.updateSubscription(subscriptionId, subscriptionData);
  }
  async cancelSubscription(subscriptionId: string) {
    // this.logger.log('CANCEL SUBSCRIPTION', subscriptionId, this.providerName);
    return this.provider.cancelSubscription(subscriptionId);
  }
  async updateSubscriptionMetadata(subscriptionId: string, metadata: any) {
    // this.logger.log(
    //   'UPDATE SUBSCRIPTION METADATA',
    //   subscriptionId,
    //   metadata,
    //   this.providerName,
    // );
    return this.provider.updateSubscriptionMetadata(subscriptionId, metadata);
  }
  async enableManualBilling(subscriptionId: string) {
    // this.logger.log('ENABLE MANUAL BILLING', subscriptionId, this.providerName);
    return this.provider.enableManualBilling(subscriptionId);
  }
  async disableManualBilling(subscriptionId: string) {
    // this.logger.log(
    //   'DISABLE MANUAL BILLING',
    //   subscriptionId,
    //   this.providerName,
    // );
    return this.provider.disableManualBilling(subscriptionId);
  }
  async listInvoices(subscriptionId: string, pagination?: PaginationDto) {
    // this.logger.log(
    //   'LIST INVOICES',
    //   subscriptionId,
    //   pagination,
    //   this.providerName,
    // );
    return this.provider.listInvoices(subscriptionId, pagination);
  }
  async getInvoice(invoiceId: string) {
    // this.logger.log('GET INVOICE', invoiceId, this.providerName);
    return this.provider.getInvoice(invoiceId);
  }
  async getSubscriptionItems(
    subscriptionId: string,
    pagination?: PaginationDto,
  ) {
    // this.logger.log(
    //   'GET SUBSCRIPTION ITEMS',
    //   subscriptionId,
    //   pagination,
    //    this.providerName,
    // );
    return this.provider.getSubscriptionItems(subscriptionId, pagination);
  }
  async getSubscriptionIncrements(
    subscriptionId: string,
    pagination?: PaginationDto,
  ) {
    // this.logger.log(
    //   'GET SUBSCRIPTION INCREMENTS',
    //   subscriptionId,
    //   pagination,
    //   this.providerName,
    // );
    return this.provider.getSubscriptionIncrements(subscriptionId, pagination);
  }
  async createOrder(orderData: PaymentGatewayTypes[T]['createOrderIn']) {
    const orderDataAdapter = this.adapter.toOrder(orderData);
    // this.logger.log('CREATE ORDER', orderDataAdapter, this.providerName);
    return this.provider.createOrder(orderDataAdapter);
  }
  async cancelOrder(orderId: string) {
    // this.logger.log('CANCEL ORDER', orderId, this.providerName);
    return this.provider.cancelOrder(orderId);
  }

  async listRecipients(pagination?: PaginationDto) {
    // this.logger.log('LIST RECIPIENTS', pagination, this.providerName);
    return this.provider.listRecipients(pagination);
  }
  async editSplit(subscriptionId: string, split: any) {
    // this.logger.log('EDIT SPLIT', subscriptionId, split, this.providerName);
    return this.provider.editSplit(subscriptionId, split);
  }
  async findCustomerDocument(document: string) {
    // this.logger.log('FIND CUSTOMER BY DOCUMENT', document, this.providerName);
    return this.provider.findCustomerDocument(document);
  }
  async findCustomerByUid(uid: string) {
    // this.logger.log('FIND CUSTOMER BY UID', uid, this.providerName);
    try {
      const response = await this.provider.findCustomerByUid(uid);
      return this.adapter.fromGetCustomer(response);
    } catch (error) {
      this.logger.error('GET CUSTOMER ERROR', error);
      return null;
    }
  }

  async findCustomerIdByUid(uid: string) {
    // this.logger.log('FIND CUSTOMER ID BY UID', uid, this.providerName);
    try {
      const response = await this.provider.findCustomerIdByUid(uid);
      return this.adapter.fromGetCustomer(response);
    } catch (error) {
      this.logger.error('GET CUSTOMER ERROR', error);
      return null;
    }
  }
}
