import { QISubscriptionItem } from 'src/modules/core/types';
import { calculateYearlyBillingDate, calculateYearlyInstallmentsAmount } from './subscription.helper';
import { PaymentMethod } from 'src/modules/payment/enum/paymentMethod.enum';
import { MONTHS } from './date.utils';

describe('Subscription Helper', () => {
  describe('calculateYearlyBillingDate', () => {
    it('should return the current month when billing day is in the future', () => {
      const billingDay = 15;

      // jan 10, 2025 (billing day 15 is in the future)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // jan 15, 2025 (same month)
      const expectedDate = new Date(2025, MONTHS.JAN, 15, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });

    it('should return the next month when billing day is in the past', () => {
      const billingDay = 5;

      // jan 10, 2025 (billing day 5 is in the past)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // feb 5, 2025 (next month)
      const expectedDate = new Date(2025, MONTHS.FEB, 5, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });

    it('should return the current month when billing day is the same as current date', () => {
      const billingDay = 10;

      // jan 10, 2025 (billing day is today)
      const currentDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();
      // jan 10, 2025 (same month)
      const expectedDate = new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime();

      const result = calculateYearlyBillingDate(billingDay, currentDate);

      expect(result).toEqual(expectedDate);
    });
  });

  describe('calculateFirstInstallmentAmount', () => {
    it('should calculate the first installment amount correctly when billing day is in the future', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 15;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        0,
        new Date(2026, MONTHS.JAN, 1, 0, 0, 0, 0).getTime(),
        PaymentMethod.PIX,
        new Date(2025, MONTHS.JAN, 1, 0, 0, 0, 0).getTime(),
      );

      expect(firstInstallmentAmount).toBe(38);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('should calculate the first installment amount correctly when billing day is in the past', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 5;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        0,
        new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
        PaymentMethod.PIX,
        new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
      );

      expect(firstInstallmentAmount).toBe(100);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('should calculate the first installment amount correctly when billing day is the same as current date', () => {
      const items = [
        { totalPrice: 200 },
        { totalPrice: 300 },
        { totalPrice: 500 },
      ] as QISubscriptionItem[];
      const installments = 10;
      const billingDay = 10;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        0,
        new Date(2026, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
        PaymentMethod.PIX,
        new Date(2025, MONTHS.JAN, 10, 0, 0, 0, 0).getTime(),
      );

      expect(firstInstallmentAmount).toBe(100);
      expect(remainingInstallmentAmount).toBe(100);
    });

    it('CalculateYearlyInstallmentsAmount', () => {
      const items = [
        {
          id: 'plan',
          name: 'Digital Starter',
          type: 'plan',
          included: 0,
          quantity: 1,
          unitPrice: 83880,
          totalPrice: 83880
        },
        {
          id: 'shotx-module',
          name: 'Shotx',
          type: 'module',
          included: 0,
          quantity: 1,
          unitPrice: 107880,
          totalPrice: 107880
        },
        {
          id: 'funnels_included',
          type: 'addon',
          name: 'Funis de Vendas',
          included: 1,
          quantity: 1,
          unitPrice: 58800,
          totalPrice: 0
        },
        {
          id: 'landing-pages_included',
          type: 'addon',
          name: 'Landing Pages',
          included: 2,
          quantity: 2,
          unitPrice: 58800,
          totalPrice: 0
        }
      ];
      const installments = 12;
      const billingDay = 20;
      const creditDiscount = 0;
      const endDate = new Date(2026, MONTHS.MAY, 20, 2, 42, 0, 0).getTime(); //30520894920; // 20/05/2026 2:25 AM UTC;
      const today = new Date(2025, MONTHS.JUN, 12, 2, 42, 0, 0).getTime(); //1749694920 // 12/06/2025 2:25 AM UTC;

      const [firstInstallmentAmount, remainingInstallmentAmount] = calculateYearlyInstallmentsAmount(
        items,
        installments,
        billingDay,
        creditDiscount,
        endDate,
        PaymentMethod.PIX,
        today,
      );

      expect(firstInstallmentAmount).toBe(4486);
      expect(remainingInstallmentAmount).toBe(15980);
    });
  });
});

