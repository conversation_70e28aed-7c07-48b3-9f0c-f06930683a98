import { motion } from "framer-motion";

interface SuccessHeaderProps {
  itemAnimation: any;
}

export const SuccessHeader = ({ itemAnimation }: SuccessHeaderProps) => {
  return (
    <motion.div variants={itemAnimation} className="space-y-4">
      <span className="inline-block px-4 py-1.5 bg-black text-transparent bg-clip-text bg-gradient-to-r from-[#e9177c] to-[#f16434] font-semibold rounded-full border border-[#e9177c]/20">
        Assinatura Ativada com Sucesso
      </span>
      <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#e9177c] to-[#f16434] whitespace-nowrap">
        Parabéns! Hora de impulsionar seus resultados!
      </h1>
      <p className="text-slate-600 dark:text-gray-300 max-w-lg mx-auto text-lg">
        Com a QI PLUS, você terá acesso a ferramentas exclusivas para alavancar
        suas vendas e alcançar resultados extraordinários.
      </p>
    </motion.div>
  );
};
