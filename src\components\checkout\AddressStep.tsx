import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAuth } from "@/contexts/auth/useAuth";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { CEPInput } from "../forms/CEPInput";
import { InputMessageError } from "../InputMessageError";
import { CheckoutFormData } from "./types";
interface AddressStepProps {
  title: string;
  toBilling: boolean;
}

export function AddressStep({ title, toBilling }: AddressStepProps) {
  const { autoCompletedAddress, countryCodes, selectedCountry, form } =
    useCheckout();
  const {
    register,
    formState: { errors },
  } = form;
  const { hasUserData } = useAuth();

  const countryInputKey = toBilling ? "billingCountry" : "country";
  const streetInputKey = toBilling ? "billingStreet" : "street";
  const numberInputKey = toBilling ? "billingNumber" : "number";
  const complementInputKey = toBilling ? "billingComplement" : "complement";
  const neighborhoodInputKey = toBilling
    ? "billingNeighborhood"
    : "neighborhood";
  const cityInputKey = toBilling ? "billingCity" : "city";
  const stateInputKey = toBilling ? "billingState" : "state";

  const cep = (
    toBilling ? form.watch("billingPostalCode") : form.watch("postalCode")
  )?.replace(/\D/g, "");
  const disableState =
    !!autoCompletedAddress?.[cep]?.uf || hasUserData(stateInputKey);
  const disableCity =
    !!autoCompletedAddress?.[cep]?.localidade || hasUserData(cityInputKey);
  const disableNeighborhood =
    !!autoCompletedAddress?.[cep]?.bairro || hasUserData(neighborhoodInputKey);
  const disableStreet =
    !!autoCompletedAddress?.[cep]?.logradouro || hasUserData(streetInputKey);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{title}</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={countryInputKey}>País</Label>
          <Select
            onValueChange={(value) => {
              const event = { target: { value } };
              register(countryInputKey).onChange(event);
            }}
            defaultValue={selectedCountry.value}
            disabled={hasUserData(countryInputKey)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Selecione um país" />
            </SelectTrigger>
            <SelectContent>
              <ScrollArea className="h-[200px]">
                {countryCodes.map(({ country, flag, value }) => (
                  <SelectItem key={country} value={value}>
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{flag}</span>
                      <span>{country}</span>
                    </div>
                  </SelectItem>
                ))}
              </ScrollArea>
            </SelectContent>
          </Select>
          <InputMessageError error={errors[countryInputKey]?.message} />
        </div>
        <div>
          <CEPInput
            register={register}
            errors={errors}
            toBilling={toBilling}
            hasUserData={hasUserData}
          />
        </div>
      </div>

      <div>
        <Label htmlFor={streetInputKey}>Rua</Label>
        <Input
          id={streetInputKey}
          {...register(streetInputKey, {
            required: {
              value: true,
              message: "Rua é obrigatório",
            },
          })}
          placeholder="Nome da rua"
          disabled={disableStreet}
        />
        <InputMessageError error={errors[streetInputKey]?.message} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={numberInputKey}>Número</Label>
          <Input
            id={numberInputKey}
            {...register(numberInputKey, {
              required: {
                value: true,
                message: "Número é obrigatório",
              },
            })}
            placeholder="123"
            disabled={hasUserData(numberInputKey)}
          />
          <InputMessageError error={errors[numberInputKey]?.message} />
        </div>

        <div>
          <Label htmlFor={complementInputKey}>Complemento</Label>
          <Input
            id={complementInputKey}
            {...register(complementInputKey, {
              required: {
                value: true,
                message: "Complemento é obrigatório",
              },
            })}
            placeholder="Apto, Sala, etc"
            disabled={hasUserData(complementInputKey)}
          />
          <InputMessageError error={errors[complementInputKey]?.message} />
        </div>
      </div>

      <div>
        <Label htmlFor={neighborhoodInputKey}>Bairro</Label>
        <Input
          id={neighborhoodInputKey}
          {...register(neighborhoodInputKey, {
            required: {
              value: true,
              message: "Bairro é obrigatório",
            },
          })}
          placeholder="Nome do bairro"
          disabled={disableNeighborhood}
        />
        <InputMessageError error={errors[neighborhoodInputKey]?.message} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label htmlFor={cityInputKey}>Cidade</Label>
          <Input
            id={cityInputKey}
            {...register(cityInputKey, {
              required: {
                value: true,
                message: "Cidade é obrigatório",
              },
            })}
            placeholder="Nome da cidade"
            disabled={disableCity}
          />
          <InputMessageError error={errors[cityInputKey]?.message} />
        </div>

        <div>
          <Label htmlFor={stateInputKey}>Estado</Label>
          <Input
            id={stateInputKey}
            {...register(stateInputKey)}
            placeholder="UF"
            disabled={disableState}
          />
          <InputMessageError error={errors[stateInputKey]?.message} />
        </div>
      </div>
    </div>
  );
}
