import { IsEnum, IsOptional, IsString } from 'class-validator';

export class AddressDto {
  @IsString()
  id?: string;

  @IsString()
  line_1: string;

  @IsOptional()
  @IsString()
  line_2?: string;

  @IsString()
  postalCode: string;

  @IsString()
  city: string;

  @IsString()
  state: string;

  @IsString()
  country: string;

  @IsEnum(['active', 'inactive'], {
    message: 'O status deve ser active ou inactive',
  })
  status?: 'active' | 'inactive';

  @IsOptional()
  created_at?: string;

  @IsOptional()
  updated_at?: string;
}
