import { Module } from '@nestjs/common';
import { AccountsModule } from 'src/modules/account/account.module';
import { AffiliateModule } from 'src/modules/affiliates/affiliate.module';

import { PagarmeModule } from 'src/modules/pagarme/pagarme.module';


import { PlanModule } from 'src/modules/plan/plan.module';
import { UpgradeController } from './upgrade.controller';

@Module({
  imports: [
    PagarmeModule,
    AccountsModule,
    PlanModule,
    AffiliateModule,
  ],
  controllers: [UpgradeController],
  exports: [],
  providers: [],
})
export class UpgradeModule { }
