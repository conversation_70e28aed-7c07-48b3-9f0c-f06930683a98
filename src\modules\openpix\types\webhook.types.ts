export interface WebhookPostBody {
  charge: ChargesItem;
  pix: PixItem;
}

export interface ChargesItem {
  _id: string;
  status: string;
  customer: null;
  value: number;
  comment: string;
  createdBy: string;
  company: string;
  transactionID: string;
  correlationID: string;
  createdAt: string;
  updatedAt: string;
}

export interface PixItem {
  _id: string;
  charge: string;
  time: string;
  value: number;
  transactionID: string;
  infoPagador: string;
  raw: Raw;
}

export interface Raw {
  horario: string;
  valor: string;
  endToEndId: string;
  txid: string;
  infoPagador: string;
}
