export const additionals = [
  { id: "shotx-module", name: "ShotX", price: 99 },
]
import { Additional } from '@/types/additional-types';


class AdditionalsRepository {
  private readonly COLLECTION_NAME = 'qiplus-additionals';

  async getAdditionals(): Promise<Additional[]> {
    try {

      // TODO: implementar busca no Firestore
      // const plansCollection = collection(db, this.COLLECTION_NAME)
      // const plansSnapshot = await getDocs(plansCollection);
      // const plans = plansSnapshot.docs.map(doc => {
      //   const data = doc.data();
      //   return {
      //     id: doc.id,
      //     status: data.status,
      //     order: data.config.order ?? 0,
      //     ...data
      //   };
      // });

      // return plans.filter(plan => plan.status === 'publish')
      //   .sort((a, b) => a.order - b.order)
      //   .map(plan => PlanModel.parse(plan));

      return [
        {
          id: "shotx-module",
          name: "<PERSON><PERSON><PERSON><PERSON>",
          price: 18990,
          discountValue: 10000,
          duration: "Mensal",
          selected: true,
          description: "Transforme seu negócio com o poder do ShotX - a ferramenta definitiva para revolucionar sua comunicação!"
        },
      ]
    } catch (error) {
      console.error('Erro ao buscar adicionais:', error);
      return [];
    }
  }
}

export const additionalsRepository = new AdditionalsRepository();
