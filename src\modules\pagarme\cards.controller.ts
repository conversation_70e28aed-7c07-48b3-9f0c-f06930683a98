import {
  Controller,
  Delete,
  Get,
  Param,
  Query,
  Request,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ResponseUtil } from 'src/utils/response';
import { FirebaseAuthGuardWithAccountAndSubscription } from '../auth/firebase-auth.guard';
import { PaymentMethod } from '../payment/enum/paymentMethod.enum';
import { PaymentGateway } from '../payment/types/gateway.types';
import { PaginationDto } from './dto/pagination.dto';
import { PagarmeService } from './pagarme.service';

@ApiTags('Cards')
@Controller('cards')
export class CardsController {
  constructor(private readonly pagarmeService: PagarmeService) {}

  @ApiOperation({ summary: 'List all cards from a customer' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccountAndSubscription)
  @ApiResponse({ status: 200, description: 'Cards found' })
  @Get()
  async getCards(@Request() req: any, @Query() pagination: PaginationDto) {
    const gtw = PaymentGateway[PaymentMethod.CREDIT_CARD];
    const customerId = req.account.customer?.[gtw];
    if (!customerId) {
      return ResponseUtil.error('Customer not found');
    }
    return this.pagarmeService.listCards(customerId, pagination);
  }

  @ApiOperation({ summary: 'Delete a card from a customer' })
  @ApiBearerAuth()
  @UseGuards(FirebaseAuthGuardWithAccountAndSubscription)
  @ApiParam({ name: 'cardId', description: 'Card ID' })
  @ApiResponse({ status: 200, description: 'Card deleted successfully' })
  @Delete(':cardId')
  async deleteCard(@Request() req: any, @Param('cardId') cardId: string) {
    const gtw = PaymentGateway[PaymentMethod.CREDIT_CARD];
    const customerId = req.account.customer?.[gtw];
    if (!customerId) {
      return ResponseUtil.error('Customer not found');
    }
    return this.pagarmeService.deleteCard(customerId, cardId);
  }
}
