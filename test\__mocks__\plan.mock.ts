export const planMock = {
  error: false,
  message: 'Plan found',
  data: {
    id: 'plan-123',
    name: 'Test Plan',
    price: 99900,
    isYearly: true,
    option: {
      contacts_min: 100,
      contacts_max: 1000,
      monthly_value: 9990,
      yearly_value: 99900,
    },
    options: [
      {
        contacts_min: 100,
        contacts_max: 1000,
        monthly_value: 9990,
        yearly_value: 99900,
      }
    ],
    modules: { shotx: false },
    levels: {},
    config: {},
    additionals: [],
    customFeatures: [],
    planValue: jest.fn().mockReturnValue(99900),
    updateFromCreatePaymentDto: jest.fn().mockReturnThis(),
    shotxValue: 0,
  },
}