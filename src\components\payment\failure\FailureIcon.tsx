import { motion } from "framer-motion";
import { XCircle } from "lucide-react";

export const FailureIcon = () => {
    return (
        <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
            className="flex justify-center"
        >
            <XCircle className="h-16 w-16 text-red-500" />
        </motion.div>
    );
}; 