import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { FirebaseService } from '../firebase/firebase.service';
import { SubscriptionService } from '../subscription/subscription.service';

@Injectable()
export class FirebaseAuthGuard implements CanActivate {
  constructor(public readonly firebaseService: FirebaseService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractToken(request);

    if (!token) {
      throw new UnauthorizedException('Token não encontrado.');
    }

    try {
      const decodedToken = await this.firebaseService.verifyIdToken(token);
      request['user'] = decodedToken; // Adiciona os dados do usuário à requisição
      return true;
    } catch (error) {
      throw new UnauthorizedException('Token inválido.');
    }
  }

  private extractToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }
}

@Injectable()
export class FirebaseAuthGuardWithAccount extends FirebaseAuthGuard {
  constructor(public readonly firebaseService: FirebaseService) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }
    const request = context.switchToHttp().getRequest<Request>();
    const user = request['user'];
    const { data: account } = await this.firebaseService.getAccountByOwner(
      user.uid,
    );

    if (account) {
      request['account'] = account;
      Logger.log('Added body account successfully.');
    }
    return true;
  }
}

@Injectable()
export class FirebaseAuthGuardWithAccountAndSubscription extends FirebaseAuthGuardWithAccount {
  constructor(firebaseService: FirebaseService) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }
    const request = context.switchToHttp().getRequest<Request>();
    const account = request['account'];

    if (account && account?.id) {
      const { gateway } =
        account.paymen_status || account.payment_upgrade_status || {};
      const subscription = account?.[gateway]?.subscription;
      if (!!subscription) {
        request['subscription'] = subscription;
        Logger.log('Added [subscription] body subscription successfully.');
      } else {
        Logger.log('Subscription not found.')
        Logger.warn('Use FirebaseAuthGuardWithAccountAndQISubscription instead if you need QISubscription.');
      }
    }
    return true;
  }
}

@Injectable()
export class FirebaseAuthGuardWithAccountAndQISubscription extends FirebaseAuthGuardWithAccount {
  constructor(
    firebaseService: FirebaseService,
    private readonly subscriptionService: SubscriptionService,
  ) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }
    const request = context.switchToHttp().getRequest<Request>();
    const account = request['account'];

    if (account && account?.id) {
      const currentSubscription =
        await this.subscriptionService.getActiveSubscription(account.id);
      if (!!currentSubscription) {
        request['currentSubscription'] = currentSubscription;
        Logger.log('Added [currentSubscription] body subscription successfully.');
      }
    }
    return true;
  }
}
