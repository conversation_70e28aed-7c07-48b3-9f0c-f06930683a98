import { createContext, useContext, useState } from "react";
import { SubscriptionProviderProps, PaymentHistoryItem, SubscriptionContextData } from "./Subscription-context-types";
import { QIScheduledAction, QISubscriptionExtended } from "@/types/backend/qiplus.types";

export const SubscriptionContext = createContext<SubscriptionContextData>({
  subscription: null,
  subscriptionHistory: [],
  setSubscription: () => { },
  setSubscriptionHistory: () => { },
  updateSubscriptionScheduledAction: () => { },
});

export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const [subscription, setSubscription] = useState<QISubscriptionExtended | null>(null);
  const [subscriptionHistory, setSubscriptionHistory] = useState<PaymentHistoryItem[]>([]);

  const updateSubscriptionScheduledAction = (id: string, scheduledAction: QIScheduledAction) => {
    if (subscription?.id === id) {
      setSubscription({ ...subscription, scheduledAction });
      setSubscriptionHistory((prev) => {
        return prev.map((item) => {
          if (item.id === id) {
            return { ...item, scheduledAction: scheduledAction };
          }
          return item;
        })
      });
    }
  };

  return (
    <SubscriptionContext.Provider
      value={{
        subscription,
        setSubscription,
        subscriptionHistory,
        setSubscriptionHistory,
        updateSubscriptionScheduledAction,
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
}

export function useSubscription() {
  const context = useContext(SubscriptionContext);

  if (!context) {
    throw new Error("useSubscription must be used within a SubscriptionProvider");
  }
  return context;
}
