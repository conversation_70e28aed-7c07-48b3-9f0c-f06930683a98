import { Account } from "src/modules/account/model/account.model";

export const mockAccount: Account = {
  id: 'account-123',
  owner: 'user-123',
  active: true,
  config: {
    automations_included: 0,
    billing: true,
    campaigns_included: 0,
    custom_plan: false,
    emails_included: 0,
    forms_included: 0,
    funnels_included: 0,
    mailboxes_included: 0,
    mailing_qiplus_disabled: false,
    shotx_included: 0,
    smtp_enabled: false,
    trial_days: 0,
  },
  planId: 'plan-123',
  post_type: 'account',
  data: {
    contacts_min: 100,
    contacts_max: 1000,
    monthly_value: 9990,
    yearly_value: 99900,
  } as any,
  levels: {} as any,
  modules: {} as any,
  title: 'Test Account',
  type: 'individual',
  status: 'active',
  payment_status: {
    status: 'paid',
    error_message: '',
    gateway: 'pagarme' as any,
    subscription_id: 'sub-123',
    planId: 'plan-123',
  },
};