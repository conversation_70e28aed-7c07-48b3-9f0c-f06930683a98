import { createContext, useContext } from "react";
import { CheckoutContextData } from "./checkout-context-types";

export const CheckoutContext = createContext<CheckoutContextData>({} as CheckoutContextData);

export function useCheckout() {
  const context = useContext(CheckoutContext);
  if (!context) {
    throw new Error("useCheckout must be used within a CheckoutProvider");
  }
  return context;
}
