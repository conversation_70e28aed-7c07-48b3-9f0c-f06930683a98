import React from "react";
import Slider from "react-slick"; // ou o componente de slider que você estiver utilizando
import { Card } from "./ui/card";
import { useNavigate } from "react-router-dom";

// Define a interface para as props do componente, usando um tipo genérico para os itens
export interface SliderCardProps<T> {
  title: string;
  sliderSettings: any; // defina um tipo mais específico conforme as configurações do seu slider
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  status?: string;
}

export const SliderCard = <T,>({
  title,
  sliderSettings,
  items = [],
  renderItem,
  status,
}: SliderCardProps<T>) => {
  const navigate = useNavigate();
  return (
    <Card className="flex flex-col p-6">
      {/* Título do componente */}
      <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">{title}</h2>

      {/* Renderiza o Slider se houver itens, caso contrário, exibe uma mensagem */}
      <div className="flex flex-col flex-grow">
        {items.length > 0 ? (
          <Slider {...sliderSettings}>
            {items.map((item, index) => (
              <div key={index} className="px-2">
                {renderItem(item, index)}
              </div>
            ))}
          </Slider>
        ) : status !== "active" ? (
          <div className="flex flex-grow flex-col justify-center items-center">
            <p className="my-auto text-center text-muted-foreground">
              Você não tem itens adicionais
            </p>
          </div>
        ) : (
          <div className="flex flex-grow flex-col justify-center items-center">
            <p className="my-auto text-center text-muted-foreground">
              Você sabia que pode personalizar seu plano?
            </p>
            <button
              onClick={() => navigate("/#plans")}
              className="text-primary"
            >
              Personalizar Plano
            </button>
          </div>
        )}
      </div>
    </Card>
  );
};

export default SliderCard;
