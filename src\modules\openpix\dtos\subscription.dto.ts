import { Type } from 'class-transformer';
import {
  IsEmail,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  Min,
  ValidateNested,
} from 'class-validator';

export class SubscriptionCustomerDto {
  @IsString()
  name: string;

  @IsString()
  taxID: string;

  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phone?: string;
}

export class CreateSubscriptionDto {
  @IsNumber()
  @Min(1)
  value: number;

  @ValidateNested()
  @Type(() => SubscriptionCustomerDto)
  customer: SubscriptionCustomerDto;

  @IsNumber()
  @Min(1)
  @Max(31)
  dayOfMonth: number;

  @IsOptional()
  @IsEnum(['MONTHLY', 'YEARLY'])
  interval?: 'MONTHLY' | 'YEARLY' = 'MONTHLY';

  @IsOptional()
  @IsEnum(['PIX', 'PAYMENT_LINK'])
  chargeType?: 'PIX' | 'PAYMENT_LINK' = 'PIX';
}

export class GetSubscriptionDto {
  @IsString()
  id: string;
}

export class ListSubscriptionsDto {
  @IsOptional()
  @IsString()
  after?: string;

  @IsOptional()
  @IsString()
  before?: string;

  @IsOptional()
  @IsString()
  first?: string;

  @IsOptional()
  @IsString()
  last?: string;
}
