import { validate<PERSON><PERSON><PERSON> } from '@/lib/utils';
import InputMask from 'react-input-mask';
import { InputMessageError } from '../InputMessageError';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

interface CNPJInputProps {
  register;
  errors;
}

export const CNPJInput = ({ register, errors }: CNPJInputProps) => (
  <div>
    <Label htmlFor="companyCnpj">CNPJ</Label>
    <InputMask
      mask="99.999.999/9999-99"
      maskChar={null}
      {...register('companyCnpj', {
        minLength: {
          value: 14,
          message: 'CNPJ inválido',
        },
        required: {
          value: true,
          message: 'CNPJ é obrigatório',
        },
        validate: (value) => {
          if (!validateCNPJ(value)) {
            return 'CNPJ inválido';
          }
          return true;
        },
      })}
    >
      {({ inputProps }) => (
        <Input
          // className={`input-class ${errors.cnpj ? 'border-red-500' : ''}`}
          {...inputProps}
          id="cnpj"
          {...register("companyCnpj")}
          placeholder="00.000.000/0000-00"
        />
      )}
    </InputMask>
    <InputMessageError error={errors.companyCnpj?.message} />
  </div>
);
