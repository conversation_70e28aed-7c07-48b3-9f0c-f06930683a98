import { motion } from "framer-motion";
import { Button } from "../../ui/button";
import { useAuth } from "@/contexts/auth/useAuth";
import { useNavigate } from "react-router-dom";

interface ActionButtonsProps {
  itemAnimation: any;
  onClose: () => void;
}

export const ActionButtons = ({
  itemAnimation,
  onClose,
}: ActionButtonsProps) => {
  const { accessSystemUrl } = useAuth();
  const navigate = useNavigate();

  const onAccessSystem = () => {
    window.open(accessSystemUrl, "_blank");
  };
  const handleSeeSubscription = () => {
    onClose();
    navigate(`/subscription`);
  };

  return (
    <motion.div
      variants={itemAnimation}
      className="flex flex-col sm:flex-row gap-3 justify-center"
    >
      {/* Fechar */}
      <Button
        type="button"
        onClick={handleSeeSubscription}
        className="w-full sm:w-auto"
        variant="outline"
      >
        Ver assinatura
      </Button>

      {/* <PERSON><PERSON>r sistema, abrir em nova aba */}
      {accessSystemUrl && (
        <Button
          type="button"
          onClick={onAccessSystem}
          className="w-full sm:w-auto"
          variant="default"
        >
          Acessar sistema
        </Button>
      )}
    </motion.div>
  );
};
