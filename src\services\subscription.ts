import { PaymentResponse } from "@/types/payment-response.type";
import { ApiService } from "./api";

export class SubscriptionService extends ApiService {
  /**
   * Processa a assinatura e retorna o feedback do pagamento
   * @param data - Dados da assinatura
   * @returns Promise<PaymentResponse> - Resposta do pagamento
   */
  async processSubscription(data): Promise<PaymentResponse> {
    return this.axios
      .post("/payment/subscribe", data)
      .then((response) => response.data)
      .catch((error) => {
        console.error("ERROR", error);
        return {
          error: error.response.data.error,
          message: error.response.data.message,
          data: null,
        };
      });
  }

  /**
   * Busca os cartões de crédito salvos do cliente
   * @param customerId - ID do cliente
   * @returns Promise<SavedCreditCard[]> - Lista de cartões salvos
   */
  async getCustomerCards(): Promise<[]> {
    try {
      const response = await this.axios.get(`/cards`);
      if (!response.data.data) {
        return [];
      }
      return response.data.data.map((card) => ({
        id: card.id,
        lastFourDigits: card.last_four_digits || card.lastFourDigits,
        brand: card.brand,
        expiryDate:
          card.exp_month && card.exp_year
            ? `${card.exp_month}/${card.exp_year}`
            : "",
        holderName: card.holder_name || card.holderName,
      }));
    } catch (error) {
      console.error("Error fetching customer cards:", error);
      return [];
    }
  }

  async deleteCustomerCard(
    customerId: string,
    cardId: string
  ): Promise<boolean> {
    try {
      await this.axios.delete(`/customers/${customerId}/cards/${cardId}`);
      return true; // Retorna true se a exclusão for bem-sucedida
    } catch (error) {
      console.error("Error deleting customer card:", error);
      return false; // Retorna false em caso de erro
    }
  }

  async getSubscriptions(): Promise<any> {
    try {
      // console.log("getSubscriptions");
      const response = await this.axios.get(`/subscriptions/my`);
      // console.log("getSubscriptions Response", response);
      if (!response) {
        return {};
      }
      return response.data;
    } catch (error) {
      console.log("Error fetching subscriptions:", error);
      return {}; // Retorna false em caso de erro
    }
  }

  async getSubscription(id: string): Promise<any> {
    try {
      const response = await this.axios.get(`/subscriptions/one/${id}`);
      if (!response.data) {
        return {};
      }
      return response.data;
    } catch (error) {
      console.log("Error fetching subscriptions:", error);
      return {}; // Retorna false em caso de erro
    }
  }

  async getInvoices(subscriptionId: string, withSubscription = false): Promise<any> {
    try {
      const response = await this.axios.get(
        `/subscriptions/${subscriptionId}/invoices/${withSubscription}`
      );
      if (!response.data) {
        return [];
      }
      return response.data;
    } catch (error) {
      console.log("Error fetching subscriptions:", error);
      return []; // Retorna false em caso de erro
    }
  }

  async cancelSubscription(subscriptionId: string): Promise<any> {
    try {
      const response = await this.axios.post(
        `/subscriptions/cancel/${subscriptionId}`
      );
      return response.data;
    } catch (error) {
      console.log("Error fetching subscriptions:", error);
      return []; // Retorna false em caso de erro
    }
  }

  async activeSubscription(subscriptionId: string): Promise<any> {
    try {
      const response = await this.axios.delete(
        `/subscriptions/cancel/${subscriptionId}`
      );
      return response.data;
    } catch (error) {
      console.log("Error fetching subscriptions:", error);
      return []; // Retorna false em caso de erro
    }
  }
  async getUrlBoleto(invoiceId: string): Promise<string> {
    try {
      const response = await this.axios.get(
        `/subscriptions/invoices/${invoiceId}/boleto`
      );

      return response.data.pdf as string;
    } catch (error) {
      console.log("Response Fetch Boleto", error);
      return "Erro ao conseguir URL"; // Retorna false em caso de erro
    }
  }
}

export const subscriptionService = new SubscriptionService();
