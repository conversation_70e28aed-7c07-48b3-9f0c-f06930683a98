import { useCheckout } from "@/contexts/checkout/useCheckout";
import { Validate } from "react-hook-form";
import InputMask from "react-input-mask";
import { CheckoutFormData } from "../checkout/types";
import { InputMessageError } from "../InputMessageError";
import { Input } from "../ui/input";
import { Label } from "../ui/label";

interface MaskedInputProps {
  inputId: keyof CheckoutFormData;
  title: string;
  mask: string;
  placeholder?: string;
  maxLength?: number;
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  patternMessage?: string;
  validate?:
    | Validate<string | number | boolean, CheckoutFormData>
    | Record<string, Validate<string | number | boolean, CheckoutFormData>>;
}

export const MaskedInput = ({
  title,
  inputId,
  mask,
  placeholder,
  maxLength,
  required,
  message,
  pattern,
  patternMessage,
  validate,
}: MaskedInputProps) => {
  const { form } = useCheckout();
  const {
    register,
    formState: { errors },
  } = form;
  return (
    <div>
      <Label htmlFor={inputId}>{title}</Label>
      <InputMask
        mask={mask}
        maskChar={null}
        id={inputId}
        {...register(inputId, {
          required: {
            value: required,
            message: message,
          },
          pattern: {
            value: pattern,
            message: patternMessage,
          },
          validate: validate,
        })}
      >
        {({ inputProps }) => (
          <Input
            // className={`input-class ${errors[inputId] ? 'border-red-500' : ''}`}
            {...inputProps}
            id={inputId}
            {...register(inputId)}
            placeholder={placeholder}
            maxLength={maxLength}
          />
        )}
      </InputMask>
      <InputMessageError error={errors[inputId]?.message} />
    </div>
  );
};
