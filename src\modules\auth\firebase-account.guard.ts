import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
  UnauthorizedException,
} from '@nestjs/common';
import { Request } from 'express';
import { FirebaseService } from '../firebase/firebase.service';
@Injectable()
export class FirebaseAccountGuard implements CanActivate {
  constructor(private readonly firebaseService: FirebaseService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractToken(request);

    if (!token) {
      throw new UnauthorizedException('Token não encontrado.');
    }

    try {
      const decodedToken = await this.firebaseService.verifyIdToken(token);
      request['user'] = decodedToken; // Adiciona os dados do usuário à requisição
      const { data: account } = await this.firebaseService.getAccountByOwner(
        decodedToken.uid,
      );
      if (!account) {
        Logger.error('Conta não encontrada.');
        throw new UnauthorizedException('Conta não encontrada.');
      }
      request['account'] = account;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Token inválido.');
    }
  }

  private extractToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }
}
