import { Test, TestingModule } from '@nestjs/testing';
import { SubscriptionRepository } from '../subscription.repository';
import { SubscriptionService } from '../subscription.service';
import {
  QIBillingInterval,
  QIPaymentMethod,
  QIScheduledAction,
  QISubscriptionStatus,
} from '../types/qiplus.types';

describe('SubscriptionService', () => {
  let service: SubscriptionService;
  let repository: SubscriptionRepository;

  const mockSubscriptionRepository = {
    create: jest.fn(),
    getSubscription: jest.fn(),
    getSubscriptionsWhere: jest.fn(),
    update: jest.fn(),
  };

  const mockTimestamp = {
    seconds: Math.floor(Date.now() / 1000),
    nanoseconds: 0,
  };

  const mockSubscription = {
    id: 'subscription-1',
    accountId: 'account-1',
    planId: 'plan-1',
    status: QISubscriptionStatus.ACTIVE,
    billingInterval: QIBillingInterval.MONTHLY,
    billingDay: 1,
    startDate: mockTimestamp,
    currentPeriodStart: mockTimestamp,
    currentPeriodEnd: mockTimestamp,
    basePrice: 100,
    incrementsPrice: 0,
    totalPrice: 100,
    paymentMethod: QIPaymentMethod.CREDIT_CARD,
    increments: [],
    nextBillingDate: mockTimestamp,
    createdAt: mockTimestamp,
    updatedAt: mockTimestamp,
  };

  const mockNewSubscription = {
    accountId: 'account-1',
    planId: 'plan-2',
    status: QISubscriptionStatus.ACTIVE,
    billingInterval: QIBillingInterval.MONTHLY,
    billingDay: 1,
    startDate: mockTimestamp,
    currentPeriodStart: mockTimestamp,
    currentPeriodEnd: mockTimestamp,
    basePrice: 200,
    incrementsPrice: 0,
    totalPrice: 200,
    paymentMethod: QIPaymentMethod.CREDIT_CARD,
    increments: [],
    nextBillingDate: mockTimestamp,
    createdAt: mockTimestamp,
    updatedAt: mockTimestamp,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SubscriptionService,
        {
          provide: SubscriptionRepository,
          useValue: mockSubscriptionRepository,
        },
      ],
    }).compile();

    service = module.get<SubscriptionService>(SubscriptionService);
    repository = module.get<SubscriptionRepository>(SubscriptionRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createSubscription', () => {
    it('should create a new subscription successfully', async () => {
      const mockDocRef = { id: 'new-subscription-id' };
      mockSubscriptionRepository.create.mockResolvedValue({
        success: true,
        data: mockDocRef,
      });

      const result = await service.createSubscription(mockNewSubscription);

      expect(result).toEqual({ success: true, data: mockDocRef });
      expect(mockSubscriptionRepository.create).toHaveBeenCalledWith(
        mockNewSubscription,
      );
    });

    it('should handle creation failure', async () => {
      mockSubscriptionRepository.create.mockResolvedValue({
        success: false,
        data: null,
      });

      const result = await service.createSubscription(mockNewSubscription);

      expect(result).toEqual({ success: false, data: null });
      expect(mockSubscriptionRepository.create).toHaveBeenCalledWith(
        mockNewSubscription,
      );
    });
  });

  describe('scheduleUpgradeSubscription', () => {
    it('should schedule upgrade subscription successfully', async () => {
      mockSubscriptionRepository.update.mockResolvedValue(true);
      mockSubscriptionRepository.create.mockResolvedValue({
        success: true,
        data: { id: 'new-subscription-id' },
      });

      const result = await service.scheduleUpgradeSubscription(
        mockSubscription.id,
        mockNewSubscription,
      );

      expect(result).toBe(true);
      expect(mockSubscriptionRepository.update).toHaveBeenCalledWith(
        mockSubscription.id,
        { scheduledAction: QIScheduledAction.CANCEL },
      );
      expect(mockSubscriptionRepository.create).toHaveBeenCalledWith({
        ...mockNewSubscription,
        status: QISubscriptionStatus.FUTURE,
        scheduledAction: QIScheduledAction.ACTIVATE,
      });
    });

    it('should handle upgrade scheduling failure', async () => {
      mockSubscriptionRepository.update.mockRejectedValue(
        new Error('Update failed'),
      );

      await expect(
        service.scheduleUpgradeSubscription(
          mockSubscription.id,
          mockNewSubscription,
        ),
      ).rejects.toThrow('Update failed');
    });
  });

  describe('scheduleDowngradeSubscription', () => {
    it('should schedule downgrade subscription successfully', async () => {
      mockSubscriptionRepository.update.mockResolvedValue(true);
      mockSubscriptionRepository.create.mockResolvedValue({
        success: true,
        data: { id: 'new-subscription-id' },
      });

      const result = await service.scheduleDowngradeSubscription(
        mockSubscription.id,
        mockNewSubscription,
      );

      expect(result).toBe(true);
      expect(mockSubscriptionRepository.update).toHaveBeenCalledWith(
        mockSubscription.id,
        { scheduledAction: QIScheduledAction.CANCEL },
      );
      expect(mockSubscriptionRepository.create).toHaveBeenCalledWith({
        ...mockNewSubscription,
        status: QISubscriptionStatus.FUTURE,
        scheduledAction: QIScheduledAction.ACTIVATE,
      });
    });

    it('should handle downgrade scheduling failure', async () => {
      mockSubscriptionRepository.update.mockRejectedValue(
        new Error('Update failed'),
      );

      await expect(
        service.scheduleDowngradeSubscription(
          mockSubscription.id,
          mockNewSubscription,
        ),
      ).rejects.toThrow('Update failed');
    });
  });

  describe('updateSubscription', () => {
    it('should update subscription status successfully', async () => {
      mockSubscriptionRepository.getSubscription.mockResolvedValue(
        mockNewSubscription,
      );
      mockSubscriptionRepository.getSubscriptionsWhere.mockResolvedValue([
        mockSubscription,
      ]);
      mockSubscriptionRepository.update.mockResolvedValue(true);

      const result = await service.updateSubscription(
        mockSubscription.accountId,
        'new-subscription-id',
      );

      expect(result).toBe(true);
      expect(mockSubscriptionRepository.update).toHaveBeenCalledTimes(2);
    });

    it('should throw error when subscription not found', async () => {
      mockSubscriptionRepository.getSubscription.mockResolvedValue(null);

      await expect(
        service.updateSubscription(
          mockSubscription.accountId,
          'non-existent-id',
        ),
      ).rejects.toThrow('Subscription not found');
    });

    it('should handle case when no active subscription exists', async () => {
      mockSubscriptionRepository.getSubscription.mockResolvedValue(
        mockNewSubscription,
      );
      mockSubscriptionRepository.getSubscriptionsWhere.mockResolvedValue([]);
      mockSubscriptionRepository.update.mockResolvedValue(true);

      const result = await service.updateSubscription(
        mockSubscription.accountId,
        'new-subscription-id',
      );

      expect(result).toBe(true);
      expect(mockSubscriptionRepository.update).toHaveBeenCalledTimes(1);
    });
  });
});
