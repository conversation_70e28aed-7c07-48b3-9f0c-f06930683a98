import { CreateCustomerDto } from 'src/modules/openpix/dtos/customer.dto';
import { OpenPixGatewayType } from 'src/modules/openpix/dtos/openpix.dto';
import { CustomerDto } from 'src/modules/pagarme/dto/customer.dto';
import { PagarmeGatewayType } from 'src/modules/pagarme/dto/pagarme.gateway.dto';
import { PagarmeService } from 'src/modules/pagarme/pagarme.service';
import { Gateway } from 'src/types/gateway.enum';
import { PaymentMethod } from '../enum/paymentMethod.enum';

export type GatewayCustomerType = {
  [PaymentMethod.PIX]: CreateCustomerDto;
  [PaymentMethod.BOLETO]: CustomerDto;
  [PaymentMethod.CREDIT_CARD]: CustomerDto;
};

export type PaymentGatewayTypes = {
  [PaymentMethod.PIX]: OpenPixGatewayType;
  [PaymentMethod.CREDIT_CARD]: PagarmeGatewayType;
  [PaymentMethod.BOLETO]: PagarmeGatewayType;
};

export const PaymentProviders = {
  [PaymentMethod.PIX]: PagarmeService,
  [PaymentMethod.BOLETO]: PagarmeService,
  [PaymentMethod.CREDIT_CARD]: PagarmeService,
};

export const PaymentGateway = {
  [PaymentMethod.PIX]: Gateway.PAGARME,
  [PaymentMethod.BOLETO]: Gateway.PAGARME,
  [PaymentMethod.CREDIT_CARD]: Gateway.PAGARME,
};
