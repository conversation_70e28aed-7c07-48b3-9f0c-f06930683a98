import { Account } from 'src/modules/account/model/account.model';
import { RegisterDto } from 'src/modules/auth/dto/register.dto';
import { CreatePaymentDto } from 'src/modules/payment/dto/create-payment.dto';
import {
  PlanPreviewDto,
  UpgradePreviewDto,
} from 'src/modules/upgrade/dto/upgrade-preview.dto';

export class PlanLevel {
  automations: number;
  forms: number;
  barcode_products: number;
  raffles: number;
  'landing-pages': number;
  live_qiplus: number;
  trackings: number;
  funnels: number;
  campaigns: number;
  questionnaires: number;
  tickets: number;
  mailing: number;
  events: number;

  constructor(data: Partial<PlanLevel>) {
    this.automations = data.automations ?? 0;
    this.forms = data.forms ?? 0;
    this.barcode_products = data.barcode_products ?? 0;
    this.raffles = data.raffles ?? 0;
    this['landing-pages'] = data['landing-pages'] ?? 0;
    this.live_qiplus = data.live_qiplus ?? 0;
    this.trackings = data.trackings ?? 0;
    this.funnels = data.funnels ?? 0;
    this.campaigns = data.campaigns ?? 0;
    this.questionnaires = data.questionnaires ?? 0;
    this.tickets = data.tickets ?? 0;
    this.mailing = data.mailing ?? 0;
    this.events = data.events ?? 0;
  }
}

export class PlanOption {
  contacts_max: number;
  yearly_value: number;
  monthly_value: number;
  contacts_min: number;

  constructor(data: Partial<PlanOption>) {
    this.contacts_max = data.contacts_max ?? 0;
    this.yearly_value = data.yearly_value ?? 0;
    this.monthly_value = data.monthly_value ?? 0;
    this.contacts_min = data.contacts_min ?? 0;
  }
}

export class PlanValue {
  implementation: number;
  'extra_landing-pages_monthly': number;
  extra_campaigns_monthly: number;
  extra_user_monthly: number;
  'extra_landing-pages_yearly': number;
  extra_shotx_yearly: number;
  extra_user_yearly: number;
  extra_qiusers_yearly: number;
  extra_shotx_monthly: number;
  extra_automations_yearly: number;
  extra_automations_monthly: number;
  extra_funnels_yearly: number;
  extra_forms_yearly: number;
  extra_event: number;
  extra_campaigns_yearly: number;
  extra_mailboxes_yearly: number;
  extra_forms_monthly: number;
  extra_qiusers_monthly: number;
  extra_funnels_monthly: number;
  extra_mailboxes_monthly: number;

  constructor(data: Partial<PlanValue>) {
    this.implementation = data.implementation ?? 0;
    this['extra_landing-pages_monthly'] =
      data['extra_landing-pages_monthly'] ?? 0;
    this.extra_campaigns_monthly = data.extra_campaigns_monthly ?? 0;
    this.extra_user_monthly = data.extra_user_monthly ?? 0;
    this['extra_landing-pages_yearly'] =
      data['extra_landing-pages_yearly'] ?? 0;
    this.extra_shotx_yearly = data.extra_shotx_yearly ?? 0;
    this.extra_user_yearly = data.extra_user_yearly ?? 0;
    this.extra_qiusers_yearly = data.extra_qiusers_yearly ?? 0;
    this.extra_shotx_monthly = data.extra_shotx_monthly ?? 0;
    this.extra_automations_yearly = data.extra_automations_yearly ?? 0;
    this.extra_automations_monthly = data.extra_automations_monthly ?? 0;
    this.extra_funnels_yearly = data.extra_funnels_yearly ?? 0;
    this.extra_forms_yearly = data.extra_forms_yearly ?? 0;
    this.extra_event = data.extra_event ?? 0;
    this.extra_campaigns_yearly = data.extra_campaigns_yearly ?? 0;
    this.extra_mailboxes_yearly = data.extra_mailboxes_yearly ?? 0;
    this.extra_forms_monthly = data.extra_forms_monthly ?? 0;
    this.extra_qiusers_monthly = data.extra_qiusers_monthly ?? 0;
    this.extra_funnels_monthly = data.extra_funnels_monthly ?? 0;
    this.extra_mailboxes_monthly = data.extra_mailboxes_monthly ?? 0;
  }
}

export class PlanConfig {
  email_option: string;
  payment_option: string;
  users_included: number;
  implementation_charge: true;
  'landing-pages_included': number;
  total_emails: string;
  events_included: number;
  plan_type: string;
  order: number;
  emails_towards_base: number;
  funnels_included: number;
  payment_methods: ['credit_card', 'boleto'];

  constructor(data: Partial<PlanConfig>) {
    this.email_option = data.email_option ?? '';
    this.payment_option = data.payment_option ?? '';
    this.users_included = data.users_included ?? 0;
    this.implementation_charge = data.implementation_charge ?? true;
    this['landing-pages_included'] = data['landing-pages_included'] ?? 0;
    this.total_emails = data.total_emails ?? '';
    this.events_included = data.events_included ?? 0;
    this.plan_type = data.plan_type ?? '';
    this.order = data.order ?? 0;
    this.emails_towards_base = data.emails_towards_base ?? 0;
    this.funnels_included = data.funnels_included ?? 0;
    this.payment_methods = data.payment_methods ?? ['credit_card', 'boleto'];
    // this.automations_included = data.automations_included || 0;
    // this.campaigns_included = data.campaigns_included || 0;
    // this.emails_included = data.emails_included || 0;
    // this.forms_included = data.forms_included || 0;
    // this.mailboxes_included = data.mailboxes_included || 0;
    // this.billing = data.billing ?? false;
  }
}

export class PlanModules {
  mailboxes: boolean;
  questionnaires: boolean;
  trackings: boolean;
  live_qiplus: boolean;
  shotx: boolean;
  qiusers: boolean;
  barcode_products: boolean;
  tickets: boolean;
  campaigns: boolean;
  events: boolean;
  raffles: boolean;
  forms: boolean;
  'landing-pages': boolean;
  funnels: boolean;
  automations: boolean;
  mailing: boolean;

  constructor(data: Partial<PlanModules>) {
    this.mailboxes = data.mailboxes ?? false;
    this.questionnaires = data.questionnaires ?? false;
    this.trackings = data.trackings ?? false;
    this.live_qiplus = data.live_qiplus ?? false;
    this.shotx = data.shotx ?? false;
    this.qiusers = data.qiusers ?? false;
    this.barcode_products = data.barcode_products ?? false;
    this.tickets = data.tickets ?? false;
    this.campaigns = data.campaigns ?? false;
    this.events = data.events ?? false;
    this.raffles = data.raffles ?? false;
    this.forms = data.forms ?? false;
    this['landing-pages'] = data['landing-pages'] ?? false;
    this.funnels = data.funnels ?? false;
    this.automations = data.automations ?? false;
    this.mailing = data.mailing ?? false;
  }
}

export interface extraValuesWithNamesInterface {
  name: string;
  value: number;
}

export interface PlanIncrement {
  id: string;
  included: number;
  extras: number;
  price: number;
}
export interface iPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  options: PlanOption[];
  customFeatures: CustomFeature[];
  planData; // Dados do plano de origem
  config: PlanConfig;
  isYearly: boolean;
  additionals: string[];
  levels: PlanLevel;
  credit?: number; // Valor do crédito da assinatura atual
  isCurrentPlan?: boolean; // Indica se é o plano atual do usuário

  // Dados para calculo de valor
  leadsCount: number;
  planMonthlyValue: number;
  planYearlyValue: number;
  extrasMonthlyValue: number;
  extrasYearlyValue: number;
  discount: number;
  discountPercentage: number;
  option: PlanOption | null;
  isCustomPlan: boolean;
  withShotx: boolean;
  increments: PlanIncrement[];
  planValue: (yearly: boolean) => number;
  totalValue: (yearly: boolean) => number;
  extrasValue: (yearly: boolean) => number;
  getShotxValue: (yearly: boolean) => number;
  extrasValueWithNames: (yearly: boolean) => extraValuesWithNamesInterface[];
}

export interface Feature {
  name: string;
  included: boolean;
}

export class Plan implements iPlan {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  options: PlanOption[];
  features: Feature[];
  customFeatures: CustomFeature[];
  planData;
  leadsCount: number;
  config: PlanConfig;
  modules: PlanModules;
  isYearly: boolean;
  additionals: string[];
  levels: PlanLevel;
  credit?: number; // Valor do crédito da assinatura atual
  isCurrentPlan?: boolean; // Indica se é o plano atual do usuário

  get increments() {
    return this.customFeatures
      .map(({ id, included, quantity, monthlyPrice, yearlyPrice }) => {
        const extras = quantity - included;
        const price =
          (this.isYearly ? yearlyPrice * 12 : monthlyPrice) * extras;
        if (extras > 0) {
          return {
            id,
            included,
            extras,
            price: Math.round((price + Number.EPSILON) * 100) / 100,
          };
        }
        return null;
      })
      .filter((feature) => feature !== null);
  }

  // Retorna o valor dos adicionais anual
  get extrasYearlyValue() {
    const total = this.customFeatures.reduce(
      (total, { yearlyPrice, quantity, included }) =>
        total + yearlyPrice * (quantity - included),
      0,
    );
    return parseFloat((total * 12).toFixed(2));
  }

  get extrasYearlyValueWithNames() {
    const customFeatureValues = this.customFeatures.map(
      ({ name, yearlyPrice, quantity, included }) => {
        return {
          name,
          value: yearlyPrice * (quantity - included),
        };
      },
    );

    const shotxValue = this.shotxValue;
    if (shotxValue > 0) {
      customFeatureValues.push({
        name: 'Shotx',
        value: shotxValue,
      });
    }
    return customFeatureValues;
  }

  get extrasMonthlyValueWithNames() {
    const customFeatureValues = this.customFeatures.map(
      ({ name, yearlyPrice, quantity, included }) => {
        return {
          name,
          value: yearlyPrice * (quantity - included),
        };
      },
    );

    const shotxValue = this.shotxValue;
    if (shotxValue > 0) {
      customFeatureValues.push({
        name: 'Shotx',
        value: shotxValue,
      });
    }
    return customFeatureValues;
  }

  // Retorna o valor dos adicionais mensal
  get extrasMonthlyValue() {
    const total = this.customFeatures.reduce(
      (total, { monthlyPrice, quantity, included }) =>
        total + monthlyPrice * (quantity - included),
      0,
    );
    return parseFloat(total.toFixed(2));
  }

  get baseValue() {
    const value = this.isYearly
      ? this.options[0].yearly_value * 12
      : this.options[0].monthly_value;
    return parseFloat(value.toFixed(2));
  }

  get option() {
    return (
      this.options.find(
        (option) =>
          this.leadsCount >= option.contacts_min &&
          this.leadsCount <= option.contacts_max,
      ) || null
    );
  }

  // Retorna o valor do plano mensal
  get planMonthlyValue() {
    // Pegar o valor pela quantidade de leads
    const price = this.option ? this.option.monthly_value : this.monthlyPrice;
    return parseFloat(price.toFixed(2));
  }

  // Retorna o valor do plano anual
  get planYearlyValue() {
    // Pegar o valor pela quantidade de leads
    const price = this.option ? this.option.yearly_value : this.yearlyPrice;
    return parseFloat((price * 12).toFixed(2));
  }

  // Retorna o valor do desconto
  get discount() {
    return this.planMonthlyValue - this.planYearlyValue;
  }

  // Retorna o valor do desconto em porcentagem
  get discountPercentage() {
    return (this.discount / this.planMonthlyValue) * 100;
  }

  get isCustomPlan() {
    return this.customFeatures.some(
      ({ quantity, included }) => quantity > included,
    );
  }

  get withShotx() {
    return this.additionals.includes('shotx-module');
  }

  get shotxValue() {
    if (this.withShotx) {
      const shotxValue = this.isYearly
        ? Number(this.planData.values['extra_shotx_yearly']) * 12
        : Number(this.planData.values['extra_shotx_monthly']);
      return parseFloat(shotxValue.toFixed(2));
    }

    return Number(0);
  }

  getShotxValue = (isYearly: boolean) => {
    const shotxValue = isYearly
      ? parseFloat(
        (Number(this.planData.values['extra_shotx_yearly']) * 12).toFixed(2),
      )
      : parseFloat(this.planData.values['extra_shotx_monthly'].toFixed(2));
    return shotxValue;
  };
  // Retorna o valor do plano anual ou mensal
  planValue = (yearly: boolean) => {
    return yearly ? this.planYearlyValue : this.planMonthlyValue;
  };

  get additionalsLeadsValue() {
    return parseFloat(
      (this.planValue(this.isYearly) - this.baseValue).toFixed(2),
    );
  }
  // Retorna o valor total do plano anual ou mensal
  totalValue = (yearly: boolean) => {
    return yearly
      ? this.planYearlyValue + this.extrasYearlyValue + this.shotxValue
      : this.planMonthlyValue + this.extrasMonthlyValue + this.shotxValue;
  };

  // Retorna o valor total dos adicionais anual ou mensal
  extrasValue = (yearly: boolean) => {
    return yearly ? this.extrasYearlyValue : this.extrasMonthlyValue;
  };

  constructor(data: Partial<Plan>) {
    this.id = data.id || '';
    this.name = data.name || '';
    this.monthlyPrice = data.monthlyPrice || 0;
    this.yearlyPrice = data.yearlyPrice || 0;
    this.options = data.options || [];
    this.customFeatures = data.customFeatures || [];
    this.planData = data.planData || null;
    this.leadsCount = data.leadsCount || 0;
    this.isYearly = data.isYearly || false;
    this.additionals = data.additionals || [];
    this.config = data.planData.config || ({} as PlanConfig);
    this.levels = data.levels || ({} as PlanLevel);
    this.credit = data.credit || 0;
    this.isCurrentPlan = data.isCurrentPlan || false;
    this.modules = data.modules || ({} as PlanModules);
  }
  extrasValueWithNames = (yearly: boolean): extraValuesWithNamesInterface[] => {
    return yearly
      ? this.extrasYearlyValueWithNames
      : this.extrasMonthlyValueWithNames;
  };

  static parse(data): Plan {
    const options = (data.options || [])
      .filter((option) => option.contacts_min > 0 && option.contacts_max > 0)
      .map((option) => ({
        contacts_min: Number(option.contacts_min),
        contacts_max: Number(option.contacts_max),
        monthly_value: Number(option.monthly_value),
        yearly_value: Number(option.yearly_value),
      })) as PlanOption[];

    const sanitizedOptions = options.reduce((options, option) => {
      // Find existing options with same monthly value
      const existingOption = options.find(
        (existing) => existing.monthly_value === option.monthly_value,
      );

      if (!existingOption) {
        options.push(option);
      } else {
        // Keep only min and max leads for same price
        const sameValueOptions = options.filter(
          (opt) => opt.monthly_value === option.monthly_value,
        );
        const minLeads = Math.min(
          option.contacts_min,
          ...sameValueOptions.map((opt) => opt.contacts_min),
        );
        const maxLeads = Math.max(
          option.contacts_max,
          ...sameValueOptions.map((opt) => opt.contacts_max),
        );

        // Remove all options with same price
        options = options.filter(
          (opt) => opt.monthly_value !== option.monthly_value,
        );

        // Add new option with min and max leads
        options.push({
          ...option,
          contacts_min: minLeads,
          contacts_max: maxLeads,
        });
      }
      return options;
    }, Array<PlanOption>());

    const { monthly_value, yearly_value, contacts_max } = sanitizedOptions[0];
    const plan = new Plan({
      id: data.id,
      name: data.title,
      monthlyPrice: monthly_value || yearly_value || 0,
      yearlyPrice: yearly_value || monthly_value || 0,
      options: sanitizedOptions || [],
      planData: data,
      leadsCount: contacts_max || 0,
      config: data.config,
      credit: data.credit || 0,
      isCurrentPlan: data.isCurrentPlan || false,
    });

    const getValue = (key: string, type: string): number => {
      key = key.replace('_included', '');
      switch (key) {
        default:
          return data.values[`extra_${key}_${type}`] as number;
      }
    };

    // Possíveis features (se não estiver aqui, não será exibida)
    const features = {
      funnels_included: 'Funis de Vendas',
      'landing-pages_included': 'Landing Pages',
      qiusers_included: 'Usuários',
    };

    Object.entries(data.config as Record<string, string>).forEach(
      ([key, value]) => {
        if (!key.includes('_included') || Number(value) < 1) return;
        const feature = features[key as keyof typeof features];
        if (!feature) return;
        plan.customFeatures.push({
          id: key,
          name: feature,
          monthlyPrice: Number(getValue(key, 'monthly')),
          yearlyPrice: Number(getValue(key, 'yearly')),
          included: Number(value),
          quantity: Number(value),
        });
      },
    );

    plan.levels = data.levels || {};
    plan.modules = data.modules || {};
    return plan;
  }

  updateFromRegisterDto(dto: RegisterDto) {
    try {
      this.leadsCount = dto.leadsCount;
      this.additionals = dto.additionals;
      this.customFeatures = this.customFeatures.map((feature) => {
        const f = dto.customs.find((f) => f.id === feature.id);
        if (!f) return feature;
        const level = feature.id.replace('_included', '');
        if (this.levels[level]) this.levels[level] = f.quantity;
        return {
          ...feature,
          quantity: f.quantity,
        };
      });
      this.isYearly = dto.isYearly;
      const newPlan = Plan.recriate(this);
      return this.updateCustomData(newPlan, dto.customs);
    } catch (error) {
      console.error(error);
    }
  }

  updateFromCreatePaymentDto(
    dto: CreatePaymentDto | UpgradePreviewDto | PlanPreviewDto,
  ): Plan {
    const hasShotx = dto.additionals.includes('shotx-module');
    this.leadsCount = dto.leadsCount;
    this.additionals = dto.additionals;
    this.customFeatures = this.customFeatures.map((feature) => {
      const f = dto.customFeatures.find((f) => f.id === feature.id);
      if (!f) return feature;
      const level = feature.id.replace('_included', '');
      if (this.levels[level]) this.levels[level] = f.quantity;
      return {
        ...feature,
        quantity: f.quantity,
      };
    });
    this.isYearly = dto.isYearly;
    this.modules.shotx = hasShotx;
    this.updateCustomData(this, dto.customFeatures);
    return Plan.recriate(this);
  }

  updateFromUpgradePreviewDto = (dto: UpgradePreviewDto) =>
    this.updateFromCreatePaymentDto(dto);

  updateFromPlanPreviewDto = (dto: PlanPreviewDto) =>
    this.updateFromCreatePaymentDto(dto);

  updateFromAccount(account: Account) {
    const { payment_status } = account;
    const { gateway } = payment_status || {};
    this.isYearly = account?.[gateway]?.subscription?.interval === 'year';
    this.leadsCount = account.data.contacts_max;
    this.customFeatures = this.customFeatures
      .map((feature) => {
        if (account.config[feature.id]) {
          return {
            ...feature,
            quantity: account.config[feature.id],
          };
        }
        return null;
      })
      .filter((feature) => feature !== null);
    this.additionals = this.additionals
      .filter((additional) => {
        const id = additional.replace('-module', '');
        if (account.modules[id]) {
          return additional;
        }
        return null;
      })
      .filter((additional) => additional !== null);
    if (account.modules.shotx) {
      this.additionals.push('shotx-module');
    }
    return this;
  }

  // Retorna os dados para o metadata
  toMetadata() {
    return {
      planIdQIPLUS: this.id,
      isYearly: this.isYearly,
      leadsCount: this.option?.contacts_max || this.leadsCount,
      additionals: this.additionals.join(','),
      features: this.customFeatures
        .map(({ id, quantity }) => `${id}:${quantity}`)
        .join(','),
    };
  }

  static recriate(plan: Plan) {
    return new Plan({
      ...plan,
      id: plan.id,
      name: plan.name,
      config: plan.planData.config,
      modules: plan.modules,
      levels: plan.levels,
    });
  }

  updateCustomData(plan: Plan, customs: { id: string; quantity: number }[]) {
    const updatedConfig = { ...plan.config };

    customs.forEach((custom) => {
      updatedConfig[custom.id] = custom.quantity;
    });

    return {
      ...plan,
      config: updatedConfig,
    };
  }

  toJson() {
    return JSON.parse(JSON.stringify(this));
  }

  static fromJson(json: string) {
    return new Plan(JSON.parse(json));
  }

  getPagarmePlanId(yearly: boolean) {
    return yearly
      ? this.planData.external_plan.pagarme.year
      : this.planData.external_plan.pagarme.month;
  }
}

export interface CustomFeature {
  id: string;
  name: string;
  monthlyPrice: number;
  yearlyPrice: number;
  quantity: number;
  included: number;
}
