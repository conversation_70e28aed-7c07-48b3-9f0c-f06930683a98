import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { CreateCustomerDto } from '../dtos/customer.dto';
import { OpenPixCustomerService } from '../services/customer.service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OpenPixCustomerService', () => {
  let service: OpenPixCustomerService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      switch (key) {
        case 'openpix.apiKey':
          return 'test-api-key';
        case 'openpix.baseUrl':
          return 'https://api.openpix.com.br/api/v1';
        default:
          return null;
      }
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpenPixCustomerService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<OpenPixCustomerService>(OpenPixCustomerService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createCustomer', () => {
    const createCustomerDto: CreateCustomerDto = {
      name: 'Test Customer',
      taxID: '12345678901',
      email: '<EMAIL>',
      address: {
        city: 'São Paulo',
        country: 'BR',
        neighborhood: 'Bairro',
        number: '123',
        street: 'Rua Teste',
        zipcode: '12345678901',
        state: 'SP',
        complement: 'Complemento',
      },
    };

    const mockResponse = {
      customer: {
        name: 'Test Customer',
        taxID: {
          taxID: '12345678901',
          type: 'BR:CPF',
        },
        email: '<EMAIL>',
        address: {
          city: 'São Paulo',
          country: 'BR',
          neighborhood: 'Bairro',
          number: '123',
          street: 'Rua Teste',
          zipcode: '12345678901',
          state: 'SP',
          complement: 'Complemento',
        },
      },
      correlationID: '123',
    };

    it('should create a customer successfully', async () => {
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: mockResponse }),
      } as any);

      const result = await service.createCustomer(createCustomerDto);
      expect(result).toEqual(mockResponse);
    });

    it('should throw an error when creation fails', async () => {
      const error = new Error('Failed to create customer');
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockRejectedValue(error),
      } as any);

      await expect(service.createCustomer(createCustomerDto)).rejects.toThrow(
        error,
      );
    });
  });
});
