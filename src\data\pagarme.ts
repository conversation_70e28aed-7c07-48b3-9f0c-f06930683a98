export const subscription = {
  brand_transaction_id: null,
  card_brand: "visa",
  card_last_digits: "0010",
  charges: 0,
  current_period_end: "2025-04-19T18:12:12.381Z",
  current_period_start: "2024-04-19T18:12:12.381Z",
  date_created: "2023-10-20T15:15:43.170Z",
  date_updated: "2024-04-19T18:12:12.382Z",
  fine: {},
  id: 1019925,
  interest: {},
  manage_token: "test_subscription_5cdMA8qyiLBsKRFYWbBZJmh5tdddU9",
  manage_url: "https://pagar.me/customers/#/subscriptions/1019925?token=test_subscription_5cdMA8qyiLBsKRFYWbBZJmh5tdddU9",
  metadata: {
    accountId: "XD2B3nuNVGPKHqt8atuu",
    implementation: "true",
    implementation_id: "**********",
    parcelas: "1",
    uid: "SVncyda1UMdZ1Jy12gMfGEWanx03",
    object: "subscription",
    payment_method: "credit_card"
  },
  phone: {
    ddd: "65",
    ddi: "55",
    id: 2372601,
    number: "*********",
    object: "phone"
  },
  plan: {
    amount: 815760,
    charges: null,
    color: null,
    date_created: "2024-04-19T18:12:11.086Z",
    days: 365,
    id: 1965771,
    installments: 1,
    invoice_reminder: null,
    name: "Plano Córtex",
    object: "plan",
    payment_deadline_charges_interval: 1,
    payment_methods: [
      "credit_card",
      "boleto"
    ],
    trial_days: 0,
    postback_url: "https://qiplus.com.br/?hook=pagarme&subject=subscription&action=update&enviroinment=development",
    settled_charges: null,
    soft_descriptor: null,
    status: "paid"
  },
  subscription_id: 1019925
}
export const pagarme = {
  days: 365,
  installments: 1,
  parcelas: 1,
  payment_method: "credit_card",
  plan_id: 1965771,
  recurrency: "yearly",
  status: "paid",
  subscription: subscription,
  parentId: ""
}