export enum PaymentMethod {
  CREDIT_CARD = "credit_card",
  BOLETO = "boleto",
  PIX = "pix",
}

export interface CheckoutFormData {
  acceptTerms: boolean;
  billingCity?: string;
  billingComplement?: string;
  billingCountry?: string;
  billingNeighborhood?: string;
  billingNumber?: string;
  billingPostalCode?: string;
  billingState?: string;
  billingStreet?: string;
  birthdate: string;
  companyName?: string;
  companyCnpj?: string;
  companyEmail?: string;
  companyPhone?: string;
  companyPhoneCountryCode?: string;
  companyAddress?: string;
  companyCountry?: string;
  paymentMethod: PaymentMethod;
  cardNumber?: string;
  cardExpiry?: string;
  cardId?: string;
  cardCvc?: string;
  cardHolderName?: string;
  companyPostalCode?: string;
  companyState?: string;
  companyStreet?: string;
  complement_billing?: string;
  discount?: number;
  installments?: string;
  isCompany: boolean;
  password?: string;
  passwordConfirm?: string;
  pixKey?: string;
  city: string;
  complement?: string;
  country: string;
  cpf: string;
  email: string;
  name: string;
  neighborhood: string;
  number: string;
  phone: string;
  phoneCountryCode: string;
  postalCode: string;
  state: string;
  street: string;
  billingDay: number;
}
