import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { Account } from 'src/modules/account/model/account.model';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { Subscription } from 'src/modules/pagarme/types/pagarme.type';

export interface AccountRequest extends Request {
  account: Account;
  subscription: Subscription;
}

@Injectable()
export class AccountInterceptorRequest implements CanActivate {
  constructor(private readonly firebaseService: FirebaseService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const request = context.switchToHttp().getRequest<AccountRequest>();
      const accountId = this.extractBodyAccountId(request);
      Logger.log('Account ID:', accountId);

      if (accountId) {
        const { data: account } =
          await this.firebaseService.getAccountByID(accountId);

        if (account) {
          request['account'] = account;
          Logger.log('Added body account successfully.');

          const { payment_status } = account;
          if (!!payment_status?.gateway) {
            const subscription = account[payment_status.gateway].subscription;
            if (!!subscription) {
              request['subscription'] = subscription;
              Logger.log('Added body subscription successfully.');
            }
          }
          return true;
        }
      }

      Logger.log('Account not found.');
      return true;
    } catch (error) {
      Logger.log('Account not found.');
      return true;
    }
  }

  private extractBodyAccountId(request: Request): string | null {
    const { metadata } = request.body?.data || {};
    return metadata?.accountId || metadata?.account_id || null;
  }
}

@Injectable()
export class AccountInterceptorBody extends AccountInterceptorRequest {
  constructor(firebaseService: FirebaseService) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    super.canActivate(context);
    const request = context.switchToHttp().getRequest<AccountRequest>();

    if (request.account) {
      request.body['account'] = request.account;

      if (request.subscription) {
        request['subscription'] = request.subscription;
      }
    }

    return true;
  }
}
