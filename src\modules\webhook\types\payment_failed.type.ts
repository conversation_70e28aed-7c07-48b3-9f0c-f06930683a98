export interface PaymentFailed {
  id: string;
  account: Account;
  type: string;
  created_at: Date;
  data: Data;
}

export interface Account {
  id: string;
  name: string;
}

export interface Data {
  id: string;
  code: string;
  url: string;
  amount: number;
  total_discount: number;
  total_increment: number;
  status: string;
  payment_method: string;
  due_at: Date;
  created_at: Date;
  items: Item[];
  customer: Customer;
  subscription: Subscription;
  cycle: Cycle;
  billing_address: BillingAddress;
  charge: Charge;
  metadata: Metadata;
}

export interface BillingAddress {
  postalCode: string;
  city: string;
  state: string;
  country: string;
  line_1: string;
  line_2: string;
}

export interface Charge {
  id: string;
  code: string;
  amount: number;
  status: string;
  currency: string;
  payment_method: string;
  due_at: Date;
  created_at: Date;
  updated_at: Date;
  last_transaction: LastTransaction;
  metadata: Metadata;
  recurrence_cycle: string;
}

export interface LastTransaction {
  operation_key: string;
  id: string;
  transaction_type: string;
  gateway_id: string;
  amount: number;
  status: string;
  success: boolean;
  installments: number;
  statement_descriptor: string;
  acquirer_name: string;
  acquirer_tid: string;
  acquirer_nsu: string;
  acquirer_message: string;
  acquirer_return_code: string;
  operation_type: string;
  card: Card;
  payment_type: string;
  created_at: Date;
  updated_at: Date;
  gateway_response: GatewayResponse;
  antifraud_response: Phones;
  metadata: Phones;
}

export interface Phones {}

export interface Card {
  id: string;
  first_six_digits: string;
  last_four_digits: string;
  brand: string;
  holder_name: string;
  exp_month: number;
  exp_year: number;
  status: string;
  type: string;
  created_at: Date;
  updated_at: Date;
  billing_address: BillingAddress;
}

export interface GatewayResponse {
  code: string;
  errors: any[];
}

export interface Metadata {
  accountId: string;
  implementation: string;
  implementation_id: string;
  parcelas: string;
  uid: string;
}

export interface Customer {
  id: string;
  name: string;
  email: string;
  code: string;
  document: string;
  document_type: string;
  type: string;
  delinquent: boolean;
  created_at: Date;
  updated_at: Date;
  birthdate: Date;
  phones: Phones;
}

export interface Cycle {
  id: string;
  start_at: Date;
  end_at: Date;
  billing_at: Date;
  status: string;
  cycle: number;
}

export interface Item {
  subscription_item_id: string;
  name: string;
  description: string;
  amount: number;
  quantity: number;
  pricing_scheme: PricingScheme;
}

export interface PricingScheme {
  price: number;
  scheme_type: string;
}

export interface Subscription {
  id: string;
  code: string;
  start_at: Date;
  interval: string;
  interval_count: number;
  billing_type: string;
  next_billing_at: Date;
  payment_method: string;
  currency: string;
  statement_descriptor: string;
  installments: number;
  status: string;
  created_at: Date;
  updated_at: Date;
  metadata: Metadata;
}
