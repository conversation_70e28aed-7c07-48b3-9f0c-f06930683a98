import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import {
  CreateSubscriptionDto,
  GetSubscriptionDto,
  ListSubscriptionsDto,
} from '../dtos/subscription.dto';
import {
  OpenPixSubscriptionList,
  OpenPixSubscriptionResponse,
} from '../interfaces/subscription.interface';

@Injectable()
export class OpenPixSubscriptionService {
  private readonly logger = new Logger(OpenPixSubscriptionService.name);
  private readonly api: AxiosInstance;

  constructor(private readonly configService: ConfigService) {
    this.api = axios.create({
      baseURL: this.configService.get<string>('openpix.baseUrl'),
      headers: {
        Authorization: this.configService.get<string>('openpix.apiKey'),
      },
    });
  }

  async createSubscription(
    dto: CreateSubscriptionDto,
  ): Promise<OpenPixSubscriptionResponse> {
    try {
      const { data } = await this.api.post<OpenPixSubscriptionResponse>(
        '/subscription',
        {
          subscription: dto,
        },
      );
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error creating subscription:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async getSubscription(
    dto: GetSubscriptionDto,
  ): Promise<OpenPixSubscriptionResponse> {
    try {
      const { data } = await this.api.get<OpenPixSubscriptionResponse>(
        `/subscription/${dto.id}`,
      );
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error getting subscription:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async listSubscriptions(
    dto: ListSubscriptionsDto,
  ): Promise<OpenPixSubscriptionList> {
    try {
      const { data } = await this.api.get<OpenPixSubscriptionList>(
        '/subscription',
        {
          params: dto,
        },
      );
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error listing subscriptions:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }
}
