import { IsNotEmpty, IsString } from 'class-validator';
export class AddressDto {
  @IsString()
  @IsNotEmpty({ message: 'Cidade é obrigatória' })
  city: string;

  @IsString()
  @IsNotEmpty({ message: 'Estado é obrigatório' })
  state: string;

  @IsString()
  @IsNotEmpty({ message: 'País é obrigatório' })
  country: string;

  @IsString()
  @IsNotEmpty({ message: 'Rua é obrigatória' })
  street: string;

  @IsString()
  @IsNotEmpty({ message: 'Numero é obrigatório' })
  number: string;

  @IsString()
  @IsNotEmpty({ message: 'Bairro é obrigatório' })
  neighborhood: string;

  @IsString()
  @IsNotEmpty({ message: 'CEP é obrigatório' })
  postalCode: string;

  @IsString()
  @IsNotEmpty({ message: 'Complemento é obrigatório' })
  complement: string;
}
