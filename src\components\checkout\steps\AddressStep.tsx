import { FieldErrors, UseFormRegister } from "react-hook-form";
import { AddressStep as AddressStepComponent } from "../AddressStep";
import { CheckoutFormData } from "../types";

interface AddressStepProps {
  title: string;
  toBilling?: boolean;
}

export function AddressStep({
  title,
  toBilling = false,
}: AddressStepProps) {
  return (
    <AddressStepComponent
      title={title}
      toBilling={toBilling}
    />
  );
}
