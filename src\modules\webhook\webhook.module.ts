import { Module } from '@nestjs/common';
import { AccountsModule } from 'src/modules/account/account.module';
import { FirebaseModule } from 'src/modules/firebase/firebase.module';
import { OpenPixModule } from 'src/modules/openpix/openpix.module';
import { PagarmeModule } from 'src/modules/pagarme/pagarme.module';
import { PlanModule } from 'src/modules/plan/plan.module';
import { WebhookController } from './webhook.controller';
import { WebhookService } from './webhook.service';

@Module({
  controllers: [WebhookController],
  providers: [WebhookService],
  imports: [
    FirebaseModule,
    PagarmeModule,
    AccountsModule,
    PlanModule,
    OpenPixModule,
  ],
})
export class WebhookModule {}
