import {
  ConflictException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { AxiosError } from 'axios';
import { DecodedIdToken } from 'firebase-admin/lib/auth/token-verifier';
import { AccountService } from 'src/modules/account/account.service';
import { OpenPixService } from 'src/modules/openpix/services/openpix.service';
import { PagarmeService } from 'src/modules/pagarme/pagarme.service';
import { PagarmeCustomerResponse } from 'src/modules/pagarme/types/pagarme.type';
import { OpenPixAdapter } from 'src/modules/payment/adapters/openpix.adapter';
import { prepareBillingAddress } from 'src/modules/payment/utils/payment.utils';
import { PlanService } from 'src/modules/plan/plan.service';
import { QiUserRepository } from 'src/modules/qiuser/qiuser.repository';
import { FirebaseService } from '../firebase/firebase.service';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly firebaseService: FirebaseService,
    private readonly accountService: AccountService,
    private readonly qiUserRepository: QiUserRepository,
    private readonly planService: PlanService,
    private readonly pagarmeService: PagarmeService,
    private readonly openpixService: OpenPixService,
  ) { }

  async authenticate(user: DecodedIdToken): Promise<{ token: string }> {
    return this.firebaseService
      .generateCustomToken(user.uid)
      .then(async (customToken) => {
        const userData = await this.qiUserRepository.findByUid(user.uid);
        if (!userData) {
          throw new Error('User not found');
        }
        const account = await this.firebaseService.getAccountByID(userData.accountId);
        if (!account.data) throw new Error('Account not found');
        const subscription = await this.accountService.getActiveSubscription(
          account.data.id,
        );
        if (userData) {
          delete userData.uid;
          delete userData.id;
          delete userData.ID;
          delete userData.keywords;
        }
        return {
          token: customToken,
          account: {
            ...account.data,
            planId: subscription?.planId || account.data.planId,
          },
          subscription: {
            ...subscription,
            nextBillingDate: subscription?.nextBillingDate
              ?.toDate()
              .toISOString(),
            startDate: subscription?.startDate?.toDate().toISOString(),
            ...(subscription?.endDate && {
              endDate: subscription?.endDate?.toDate().toISOString(),
            }),
            currentPeriodStart: subscription?.currentPeriodStart
              ?.toDate()
              .toISOString(),
            currentPeriodEnd: subscription?.currentPeriodEnd
              ?.toDate()
              .toISOString(),
            endDate: subscription?.endDate?.toDate().toISOString(),
          },
          userData,
        };
      })
      .catch((error) => {
        console.error('Error generating custom token:', error);
        throw new UnauthorizedException('Invalid Firebase ID Token');
      });
  }

  async register(dto: RegisterDto): Promise<
    | {
      uid: string;
      accountId: string;
      qiUserId: string;
      token: string;
    }
    | { error: boolean; message: string }
  > {
    const { email, password, name, isCompany, birthdate } = dto;
    const {
      data: plan,
      error: planError,
      message: planMessage,
    } = await this.planService.getPlanById(dto.planId);
    if (planError || !plan) {
      console.error(planMessage);
      throw new Error(planMessage);
    }
    console.log('Plan found');

    plan.updateFromRegisterDto(dto);

    const {
      data: auth,
      error: authError,
      message: authMessage,
    } = await this.firebaseService.createUser(email, password);
    if (authError) {
      console.error(authMessage);
      throw new ConflictException(authMessage);
    }

    const uid = auth!.uid;

    console.log('User created');

    const pagarmeCustomer = (await this.pagarmeService.createCustomer({
      name,
      email,
      document: isCompany ? dto.company.cnpj : dto.cpf,
      document_type: isCompany ? 'cnpj' : 'cpf',
      type: isCompany ? 'company' : 'individual',
      code: uid,
      birthdate,
      address: prepareBillingAddress(dto.address),
    })) as PagarmeCustomerResponse;

    if (pagarmeCustomer.error || !pagarmeCustomer.id) {
      this.firebaseService.deleteUser(uid);
      console.error(pagarmeCustomer.message);
      return {
        error: true,
        message: pagarmeCustomer.message || 'Error creating Pagarme customer',
      };
    }

    console.log('Pagarme customer created');

    const adaptedCustomer = new OpenPixAdapter().toCreateCustomer({
      ...dto,
      uid,
    });
    const openpixCustomer =
      await this.openpixService.createCustomer(adaptedCustomer);

    if (
      openpixCustomer instanceof AxiosError ||
      !openpixCustomer.customer.correlationID
    ) {
      this.pagarmeService.updateCustomer(pagarmeCustomer.id, {
        ...pagarmeCustomer.data!,
        metadata: {
          failed_to_create_openpix_customer: true,
        },
      });
      this.firebaseService.deleteUser(uid);
      console.error('Error creating OpenPix customer');
      return { error: true, message: 'Error creating OpenPix customer' };
    }
    console.log('OpenPix customer created');

    const openpixCustomerId = openpixCustomer.customer.correlationID;

    const {
      data: account,
      error: accountError,
      message: accountMessage,
    } = await this.accountService.create(
      uid,
      dto,
      plan,
      pagarmeCustomer.id,
      openpixCustomerId,
    );
    if (accountError) {
      console.error(accountMessage);
      throw new Error(accountMessage);
    }

    const accountId = account!.id;

    console.log('Account created');
    const {
      data: qiUser,
      error: qiUserError,
      message: qiUserMessage,
    } = await this.qiUserRepository.create(
      uid,
      account!.id,
      auth!.photoURL || '',
      dto,
    );

    if (qiUserError) {
      console.error(qiUserMessage);
      throw new Error(qiUserMessage);
    }

    console.log('QiUser created');
    // Create custom token
    const token = await this.firebaseService.generateCustomToken(uid);

    console.log('Custom token created');
    return {
      uid,
      accountId,
      qiUserId: qiUser!.id as string,
      token,
    };
  }

  async getToken(uid: string): Promise<{ token: string }> {

    const token = await this.firebaseService.generateCustomToken(uid);
    return { token };
  }
}
