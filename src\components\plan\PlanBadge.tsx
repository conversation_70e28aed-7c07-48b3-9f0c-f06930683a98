import { cn } from "@/lib/utils";

interface PlanBadgeProps {
  tagIcon?: React.ReactNode;
  tag: string;
}

export function PlanBadge({ tagIcon, tag }: PlanBadgeProps) {

  const baseTagClasses = `
    inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
    transition-all duration-300 hover:scale-105
    bg-blue-100 text-blue-700 border border-blue-300
    hover:bg-blue-200 hover:border-blue-400
  `;

  return (
    <div className={cn(baseTagClasses)}>

      <div className="flex items-center gap-1">
        {tagIcon}
        <span>{tag}</span>
      </div>
    </div>
  );
}