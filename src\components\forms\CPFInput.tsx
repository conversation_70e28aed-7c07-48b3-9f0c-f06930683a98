import { CheckoutFormData } from '@/components/checkout/types';
import { validateCPF } from '@/lib/utils';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import InputMask from 'react-input-mask';
import { InputMessageError } from '../InputMessageError';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

interface CPFInputProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  disabled?: boolean;
}

export const CPFInput = ({ register, errors, disabled }: CPFInputProps) => {
  if (disabled) {
    return (
      <div>
        <Label htmlFor="cpf">CPF</Label>
        <Input
          disabled={disabled}
          readOnly
          {...register('cpf')}
        />
      </div>
    )
  }

  return (
    <div>
      <Label htmlFor="cpf">CPF</Label>
      <InputMask
        mask="999.999.999-99"
        maskChar={null}
        {...register('cpf', {
          minLength: {
            value: 14,
            message: 'CPF inválido',
          },
          required: {
            value: true,
            message: 'CPF é obrigatório',
          },
          validate: (value) => {
            if (!validateCPF(value)) {
              return 'CPF inválido';
            }
            return true;
          },
        })}
      >
        {({ inputProps }) => (
          <Input
            // className={`input-class ${errors.cpf ? 'border-red-500' : ''}`}
            {...inputProps}
            id="cpf"
            {...register("cpf")}
            placeholder="000.000.000-00"
          />
        )}
      </InputMask>
      <InputMessageError error={errors.cpf?.message} />
    </div>
  );
};
