import { ActionButtons } from "@/components/payment/success/ActionButtons";
import { SuccessIcon } from "@/components/payment/success/SuccessIcon";
import { SupportFooter } from "@/components/payment/SupportFooter";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { motion } from "framer-motion";

interface SkipPaymentSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SkipPaymentSuccess = ({ isOpen, onClose }: SkipPaymentSuccessModalProps) => {
  const { selectedPlan } = useCheckout();

  const containerAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.25,
      },
    },
  };

  const itemAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full h-screen max-w-full p-0 bg-transparent border-none flex items-center justify-center overflow-y-auto custom-scrollbar"
        aria-describedby={undefined}
        showCloseButton={false}
      >
        <style>
          {`
            .dialog-overlay {
              background-color: rgba(255, 255, 255, 0.2) !important;
              backdrop-filter: blur(4px);
            }
          `}
        </style>
        <motion.div
          variants={containerAnimation}
          initial="hidden"
          animate="visible"
          className="w-full h-full relative z-10 flex items-center justify-center"
        >
          <Card className="w-[60%] max-h-[90vh] overflow-y-auto p-8 space-y-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-slate-200/50 shadow-2xl">
            <DialogTitle>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="flex justify-center"
              >
                <img
                  src="/qi-plus-brand.png"
                  alt="QI PLUS Logo"
                  className="h-14 w-auto px-4 py-2"
                />
              </motion.div>
            </DialogTitle>

            <div className="text-center space-y-2">
              <SuccessIcon />
              <SkipPaymentSuccessHeader itemAnimation={itemAnimation} />
            </div>

            <motion.div
              variants={itemAnimation}
              className="bg-gradient-to-r from-[#e9177c]/10 to-[#f16434]/10 p-6 rounded-xl border border-[#e9177c]/20 text-center"
            >
              <h3 className="text-xl font-semibold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-[#e9177c] to-[#f16434]">
                Alteração Agendada com Sucesso
              </h3>
              <p className="text-slate-600 dark:text-gray-300">
                {selectedPlan?.name && (
                  <>
                    Seu upgrade para o plano <strong>{selectedPlan.name}</strong> foi agendado com sucesso.
                    <br />
                    O novo plano entrará em vigor na data da sua próxima cobrança.
                    <br />
                    Até lá, você continuará com seu plano atual sem alterações.
                  </>
                )}
              </p>
            </motion.div>

            <ActionButtons itemAnimation={itemAnimation} onClose={onClose} />
            <SupportFooter itemAnimation={itemAnimation} />
          </Card>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

// Custom header component for skip payment success
const SkipPaymentSuccessHeader = ({ itemAnimation }: { itemAnimation: any }) => {
  return (
    <motion.div variants={itemAnimation} className="space-y-4">
      <span className="inline-block px-4 py-1.5 bg-black text-transparent bg-clip-text bg-gradient-to-r from-[#e9177c] to-[#f16434] font-semibold rounded-full border border-[#e9177c]/20">
        Upgrade Agendado com Sucesso
      </span>
      <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-[#e9177c] to-[#f16434]">
        Parabéns pelo seu agendamento!
      </h1>
      <p className="text-slate-600 dark:text-gray-300 max-w-lg mx-auto text-lg">
        Sua solicitação de upgrade foi processada com sucesso e será aplicada
        automaticamente na data da sua próxima cobrança.
      </p>
    </motion.div>
  );
};

export default SkipPaymentSuccess;
