import { useState, useEffect } from "react";
import { Timer } from "lucide-react";
import { Badge } from "@/components/ui/badge";

export function CountdownTimer() {
  const [timeLeft, setTimeLeft] = useState({
    hours: 4,
    minutes: 59,
    seconds: 59
  });

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prevTime => {
        if (prevTime.seconds > 0) {
          return { ...prevTime, seconds: prevTime.seconds - 1 };
        } else if (prevTime.minutes > 0) {
          return { ...prevTime, minutes: prevTime.minutes - 1, seconds: 59 };
        } else if (prevTime.hours > 0) {
          return { hours: prevTime.hours - 1, minutes: 59, seconds: 59 };
        } else {
          clearInterval(timer);
          return prevTime;
        }
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <Badge 
      variant="outline" 
      className="animate-pulse bg-primary/5 px-4 py-2 text-lg font-semibold rounded-lg border-2 border-primary/20 hover:bg-primary/10 transition-colors"
    >
      <Timer className="w-5 h-5 mr-2 text-primary" />
      {String(timeLeft.hours).padStart(2, '0')}:{String(timeLeft.minutes).padStart(2, '0')}:{String(timeLeft.seconds).padStart(2, '0')}
    </Badge>
  );
}