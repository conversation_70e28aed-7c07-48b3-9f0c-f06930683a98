import React from "react";
import { Card } from "../ui/card";

// Define o formato dos itens que serão exibidos
export interface InfoItem {
  label: string;
  value: React.ReactNode | string;
  append?: React.ReactNode;
}

// Define as props do componente, incluindo o título e os itens
export interface InfoCardProps {
  title: string;
  items: InfoItem[];
}

// Componente genérico que exibe um Card com título e lista de itens
export const InfoCard: React.FC<InfoCardProps> = ({ title, items }) => {
  return (
    <Card className="p-6">
      <h2 className="font-semibold mb-4 uppercase tracking-wide text-blue-500 ">{title}</h2>
      <div
        className={`grid grid-cols-${items.length > 1 ? "2" : "1"} gap-4 mb-6`}
      >
        {items.map((item, index) => (
          <div key={index}>
            <p className="text-sm text-muted-foreground">{item.label}</p>
            <p className="font-medium">{item.value}{item.append}</p>
          </div>
        ))}
      </div>
    </Card>
  );
};

export default InfoCard;
