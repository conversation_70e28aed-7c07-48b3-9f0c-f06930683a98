import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common';
import { Request } from 'express';
import { FirebaseService } from '../firebase/firebase.service';
import { SubscriptionService } from '../subscription/subscription.service';

@Injectable()
export class OptionalFirebaseAuthGuard implements CanActivate {
  constructor(public readonly firebaseService: FirebaseService) { }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractToken(request);

    if (!token) {
      // No token provided, but that's okay - we'll mark the request as unauthenticated
      request['isAuthenticated'] = false;
      return true;
    }

    try {
      const decodedToken = await this.firebaseService.verifyIdToken(token);
      request['user'] = decodedToken;
      request['isAuthenticated'] = true;
      return true;
    } catch (error) {
      // Invalid token, but we'll still allow the request
      request['isAuthenticated'] = false;
      return true;
    }
  }

  private extractToken(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }
}

@Injectable()
export class OptionalFirebaseAuthGuardWithAccount extends OptionalFirebaseAuthGuard {
  constructor(public readonly firebaseService: FirebaseService) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }

    const request = context.switchToHttp().getRequest<Request>();

    if (!request['isAuthenticated']) {
      // If not authenticated, we don't need to fetch account
      return true;
    }

    const user = request['user'];
    const { data: account } = await this.firebaseService.getAccountByOwner(
      user.uid,
    );

    if (account) {
      request['account'] = account;
      Logger.log('Added body account successfully.');
    }
    return true;
  }
}

@Injectable()
export class OptionalFirebaseAuthGuardWithAccountAndSubscription extends OptionalFirebaseAuthGuardWithAccount {
  constructor(
    firebaseService: FirebaseService,
    private readonly subscriptionService: SubscriptionService,
  ) {
    super(firebaseService);
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const canActivate = await super.canActivate(context);
    if (!canActivate) {
      return false;
    }

    const request = context.switchToHttp().getRequest<Request>();

    if (!request['isAuthenticated']) {
      // If not authenticated, we don't need to fetch subscription
      return true;
    }

    const account = request['account'];

    if (account && account?.id) {
      const currentSubscription =
        await this.subscriptionService.getActiveSubscription(account.id);
      if (!!currentSubscription) {
        request['currentSubscription'] = currentSubscription;
        Logger.log('Added body subscription successfully.');
      }
    }
    return true;
  }
}
