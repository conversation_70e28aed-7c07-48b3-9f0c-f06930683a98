import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaymentService } from './payment.service';
import { FirebaseService } from 'src/modules/firebase/firebase.service';
import { AccountService } from 'src/modules/account/account.service';
import { PlanService } from 'src/modules/plan/plan.service';
import { AffiliateService } from 'src/modules/affiliates/affiliate.service';
import { UpgradeService } from 'src/modules/upgrade/upgrade.service';
import { SubscriptionService } from 'src/modules/subscription/subscription.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Account } from 'src/modules/account/model/account.model';
import { GatewayService } from './gateway.service';
import { QIPaymentMethod } from 'src/modules/subscription/types/qiplus.types';
import { mockAccount } from 'test/__mocks__/account.mock';
import { mockCreatePaymentDto } from 'test/__mocks__/payment-dto.mock';
import { mockSubscription } from 'test/__mocks__/subscription.mock';
import { planMock } from 'test/__mocks__/plan.mock';

// Mock the formatDataToCreditCard function
jest.mock('./utils/payment.utils', () => ({
  formatDataToCreditCard: jest.fn().mockImplementation(() => ({
    number: '****************',
    holder_name: 'Test User',
    exp_month: '12',
    exp_year: '2030',
    cvv: '123',
    customer_id: 'customer-123',
    billing_address: {
      city: 'Test City',
      line_2: 'Apt 123',
      line_1: '123,Test Street,Test Neighborhood',
      postalCode: '********',
      state: 'TS',
      country: 'BR',
    },
  })),
  calculateDueDate: jest.fn().mockImplementation(() => new Date().toISOString()),
}));

// Mock the splitNumbers function
jest.mock('src/utils/pagarme', () => ({
  splitNumbers: jest.fn().mockReturnValue([12, 2030]),
  returnOnlyNumbers: jest.fn().mockImplementation(str => str),
}));

// Mock GatewayService
jest.mock('./gateway.service');

describe('PaymentService', () => {
  let service: PaymentService;

  // Mock data

  // Mock implementations
  const mockFirebaseService = {
    // Add methods as needed
  };

  const mockAccountService = {
    getAccount: jest.fn().mockImplementation((accountId) => {
      console.log('Mock AccountService getAccount called with:', accountId);
      if (accountId === 'account-123') {
        return Promise.resolve(mockAccount);
      }
      return Promise.resolve(null);
    }),
  };

  const mockPlanService = {
    getPlanById: jest.fn().mockImplementation((planId) => {
      console.log('Mock PlanService getPlanById called with:', planId);
      if (planId === 'plan-123') {
        return Promise.resolve(planMock);
      }
      return Promise.resolve(null);
    }),
  };

  const mockAffiliateService = {
    // Add methods as needed
  };

  const mockUpgradeService = {
    processUpgradeFromSubscription: jest.fn().mockImplementation(() => {
      return Promise.resolve({
        error: false,
        message: 'Upgrade processed successfully',
        data: {
          id: 'subscription-456',
        },
      });
    }),
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key) => {
      const config = {
        'pagarme.apiKey': 'test-api-key',
        'pagarme.baseUrl': 'https://api.test.com',
      };
      return config[key];
    }),
  };

  const mockSubscriptionService = {
    getActiveSubscription: jest.fn().mockImplementation((accountId) => {
      if (accountId === 'account-123-with-subscription') {
        return Promise.resolve(mockSubscription);
      }
      return Promise.resolve(null);
    }),
    createSubscription: jest.fn().mockImplementation(() => {
      return Promise.resolve({
        error: false,
        message: 'Subscription created successfully',
        data: {
          id: 'subscription-123',
        },
      });
    }),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        { provide: FirebaseService, useValue: mockFirebaseService },
        { provide: AccountService, useValue: mockAccountService },
        { provide: PlanService, useValue: mockPlanService },
        { provide: AffiliateService, useValue: mockAffiliateService },
        { provide: UpgradeService, useValue: mockUpgradeService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: SubscriptionService, useValue: mockSubscriptionService },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);

    // Reset GatewayService mock
    (GatewayService as jest.Mock).mockClear();
  });
  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('subscribe', () => {
    const mockGatewayInstance = {
      findCustomerByUid: jest.fn(),
      createCustomer: jest.fn(),
      updateCustomer: jest.fn(),
      createCard: jest.fn(),
      createOrder: jest.fn(),
    };

    beforeEach(() => {
      // Reset and setup GatewayService mock for each test
      (GatewayService as jest.Mock).mockImplementation(() => mockGatewayInstance);
    });

    it('should process an upgrade if there is an active subscription', async () => {
      // Setup
      const dto = { ...mockCreatePaymentDto as CreatePaymentDto, accountId: 'account-123-with-subscription' };

      // Execute
      const result = await service.subscribe(dto, mockAccount as Account);

      // Verify
      expect(mockSubscriptionService.getActiveSubscription).toHaveBeenCalledWith('account-123-with-subscription');
      expect(mockUpgradeService.processUpgradeFromSubscription).toHaveBeenCalledWith(
        mockSubscription,
        mockAccount,
        dto
      );
      expect(result).toEqual({
        error: false,
        message: 'Upgrade processed successfully',
        data: {
          id: 'subscription-456',
        },
      });
    });

    it('should create a new subscription when there is no active subscription', async () => {
      // Setup
      const dto = { ...mockCreatePaymentDto as CreatePaymentDto };

      // Mock customer creation
      mockGatewayInstance.findCustomerByUid.mockResolvedValue(null);
      mockGatewayInstance.createCustomer.mockResolvedValue({
        customer: { id: 'customer-123' }
      });

      // Mock card creation
      mockGatewayInstance.createCard.mockResolvedValue({
        id: 'card-123'
      });

      // Mock order creation
      mockGatewayInstance.createOrder.mockResolvedValue({
        error: false,
        data: {
          id: 'order-123',
          customer: { id: 'customer-123' },
          card: { id: 'card-123' }
        }
      });

      // Execute
      const result = await service.subscribe(dto, mockAccount as Account);

      // Verify
      expect(mockSubscriptionService.getActiveSubscription).toHaveBeenCalledWith('account-123');
      expect(mockPlanService.getPlanById).toHaveBeenCalledWith('plan-123');
      expect(mockSubscriptionService.createSubscription).toHaveBeenCalled();
      expect(mockGatewayInstance.createOrder).toHaveBeenCalled();
      expect(result.error).toBeFalsy();
      expect(result.data).toBeDefined();
    });

    it('should throw an error if plan is not found', async () => {
      // Setup
      const dto = { ...mockCreatePaymentDto as CreatePaymentDto, planId: 'non-existent-plan' };

      // Mock customer creation
      mockGatewayInstance.findCustomerByUid.mockResolvedValue(null);
      mockGatewayInstance.createCustomer.mockResolvedValue({
        customer: { id: 'customer-123' }
      });

      // Execute & Verify
      await expect(service.subscribe(dto, mockAccount as Account)).rejects.toThrow();
    });
  });
  describe('processPayment', () => {
    const mockGatewayInstance = {
      findCustomerByUid: jest.fn(),
      createOrder: jest.fn(),
    };

    beforeEach(() => {
      (GatewayService as jest.Mock).mockImplementation(() => mockGatewayInstance);
      jest.clearAllMocks();

      mockAccountService.getAccount = jest.fn().mockResolvedValue(mockAccount);
      mockGatewayInstance.findCustomerByUid.mockResolvedValue({ id: 'customer-123' });
      mockGatewayInstance.createOrder.mockResolvedValue({
        error: false,
        message: 'Order created successfully',
        data: { id: 'order-123' },
      });
    });

    it('should process payment for a subscription with existing customer ID', async () => {
      const result = await service.processPayment(mockSubscription);

      expect(mockAccountService.getAccount).toHaveBeenCalledWith('account-123');
      expect(mockGatewayInstance.createOrder).toHaveBeenCalled();
      expect(result).toEqual({
        error: false,
        message: 'Pedido de renovação criado com sucesso',
        data: { id: 'order-123' },
      });
    });

    it('should find customer by UID if customerId is not provided', async () => {
      const subscriptionWithoutCustomerId = {
        ...mockSubscription,
        customerId: undefined,
      };

      const result = await service.processPayment(subscriptionWithoutCustomerId);

      expect(mockAccountService.getAccount).toHaveBeenCalledWith('account-123');
      expect(mockGatewayInstance.findCustomerByUid).toHaveBeenCalledWith('user-123');
      expect(mockGatewayInstance.createOrder).toHaveBeenCalled();
      expect(result).toEqual({
        error: false,
        message: 'Pedido de renovação criado com sucesso',
        data: { id: 'order-123' },
      });
    });

    it('should return early if credit card payment method but no card ID', async () => {
      const subscriptionWithoutCardId = {
        ...mockSubscription,
        paymentMethod: QIPaymentMethod.CREDIT_CARD,
        cardId: undefined,
      };

      await expect(service.processPayment(subscriptionWithoutCardId)).resolves.toEqual({
        error: true,
        message: 'Cartão de crédito não associado à assinatura',
      });

      expect(mockAccountService.getAccount).not.toHaveBeenCalled();
      expect(mockGatewayInstance.createOrder).not.toHaveBeenCalled();
    });

    it('should throw an error if account is not found', async () => {
      mockAccountService.getAccount = jest.fn().mockResolvedValue(null);

      const subscriptionWithNonExistentAccount = {
        ...mockSubscription,
        accountId: 'non-existent-account',
      };

      await expect(service.processPayment(subscriptionWithNonExistentAccount)).resolves.toEqual({
        error: true,
        message: 'Conta não encontrada',
      });

      expect(mockGatewayInstance.createOrder).not.toHaveBeenCalled();
    });

    it('should throw an error if customer is not found', async () => {
      mockGatewayInstance.findCustomerByUid.mockResolvedValue(null);

      const subscriptionWithoutCustomerId = {
        ...mockSubscription,
        customerId: undefined,
      };

      await expect(service.processPayment(subscriptionWithoutCustomerId))
        .resolves
        .toEqual({
          error: true,
          message: 'Customer não encontrado',
        });

      expect(mockGatewayInstance.createOrder).not.toHaveBeenCalled();
    });

    it('should throw an error if gateway returns error', async () => {
      mockGatewayInstance.createOrder.mockResolvedValue({
        error: true,
        message: 'Gateway error',
        data: null,
      });
      const subscriptionWithError = {
        ...mockSubscription,
        paymentMethod: undefined,
      } as any;
      await expect(service.processPayment(subscriptionWithError))
        .resolves
        .toEqual({
          error: true,
          message: 'Método de pagamento não suportado',
        });
      expect(mockGatewayInstance.createOrder).not.toHaveBeenCalled();
    });
  });

});
