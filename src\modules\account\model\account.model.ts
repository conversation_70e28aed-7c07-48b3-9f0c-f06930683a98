import { Subscription } from 'src/modules/pagarme/types/pagarme.type';
import { PaymentStatus } from 'src/modules/payment/entities/paymentStatus.entity';
import {
  PlanLevel,
  PlanModules,
  PlanOption,
} from 'src/modules/plan/model/plan.model';
export type OpenPix = {
  status: string;
  parentId?: string;
  customerId?: string;
  subscriptionId?: string;
  subscription?: Subscription;
};

export type Pagarme = {
  status: string;
  parentId?: string;
  customerId?: string;
  subscriptionId?: string;
  subscription?: Subscription;
};

export class Account {
  id?: string;
  owner: string;
  active: boolean;
  config: {
    automations_included: number;
    billing: boolean;
    campaigns_included: number;
    custom_plan: boolean;
    email_option?: string;
    emails_included: number;
    emails_towards_base?: number;
    events_included?: number;
    forms_included?: number;
    funnels_included: number;
    implementation_charge?: boolean;
    landing_pages_included?: number;
    mailboxes_included: number;
    mailing_qiplus_disabled: boolean;
    payment_method?: string;
    plan_type?: string;
    quiusers_included?: number;
    smtp_enabled: boolean;
    trial_days: number;
    shotx_included: number;
  };
  planId: string;
  post_type: string;
  data: PlanOption;
  levels: PlanLevel;
  modules: PlanModules;
  title: string;
  type: 'corporation' | 'individual';
  status: string;
  payment_status: PaymentStatus;
  payment_upgrade_status?: PaymentStatus;
  affiliateId?: string;
  parentId?: string;
  subscription?: Subscription;
  openpix?: OpenPix;
  pagarme?: Pagarme;
  customer?: {
    pagarme?: string;
    openpix?: string;
  };
}
