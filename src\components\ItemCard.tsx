
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCurrency } from "@/lib/utils";
import { CheckCircle } from "lucide-react";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";

export const ItemCard = ({ item }: { item: any }) => {
  return (
    <TooltipProvider>
      <Card className="bg-muted rounded-lg mx-2">
        <CardHeader className="flex flex-row justify-between items-center">
          <div>
            <CardTitle className="text-md">
              {item.name}
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground mt-2">
              {formatCurrency(item.totalPrice)}
            </CardDescription>
          </div>
          {item.type === 'addon' && (
            <Tooltip>
              <TooltipTrigger>
                <Badge variant="outline" className="text-sm font-medium">
                  {item.quantity}
                </Badge>
              </TooltipTrigger>
              <TooltipContent
                side="top"
                align="center"
                sideOffset={8}
                className="max-w-xs border border-white rounded-md p-2"
                avoidCollisions={true}
                collisionPadding={10}
              >
                <div className="space-y-1">
                  <p>incluídos: {item.included}</p>
                  <p>extras: {item.quantity - item.included}</p>
                </div>
              </TooltipContent>
            </Tooltip>
          )}
          {item.type === 'module' && (
            <Tooltip>
              <TooltipTrigger>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </TooltipTrigger>
              <TooltipContent
                side="top"
                align="center"
                sideOffset={8}
                className="border border-white rounded-md p-2"
                avoidCollisions={true}
                collisionPadding={10}
              >
                <p>Ativo</p>
              </TooltipContent>
            </Tooltip>
          )}
        </CardHeader>
      </Card>
    </TooltipProvider>
  );
};
