import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { OpenPixWebhook } from '../interfaces/webhook.interface';
import { OpenPixWebhookService } from '../services/webhook.service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OpenPixWebhookService', () => {
  let service: OpenPixWebhookService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      switch (key) {
        case 'openpix.apiKey':
          return 'test-api-key';
        case 'openpix.baseUrl':
          return 'https://api.openpix.com.br/api/v1';
        case 'openpix.webhookUrl':
          return 'https://api.yourdomain.com/webhooks/openpix';
        case 'openpix.webhookSecret':
          return 'test-webhook-secret';
        default:
          return null;
      }
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpenPixWebhookService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<OpenPixWebhookService>(OpenPixWebhookService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerWebhook', () => {
    const webhook: OpenPixWebhook = {
      name: 'Test Webhook',
      event: 'OPENPIX:CHARGE_CREATED',
      url: 'https://api.yourdomain.com/webhooks/openpix',
      authorization: 'test-webhook-secret',
      isActive: true,
    };

    const mockResponse = {
      webhook: {
        ...webhook,
        id: 'webhook_123',
        createdAt: '2024-03-01T00:00:00Z',
        updatedAt: '2024-03-01T00:00:00Z',
      },
    };

    it('should register a webhook successfully', async () => {
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: mockResponse }),
      } as any);

      const result = await service.registerWebhook(webhook);
      expect(result).toEqual(mockResponse);
    });

    it('should throw an error when registration fails', async () => {
      const error = new Error('Failed to register webhook');
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockRejectedValue(error),
      } as any);

      await expect(service.registerWebhook(webhook)).rejects.toThrow(error);
    });
  });

  describe('handleWebhookPayload', () => {
    const mockPayload = {
      charge: {
        status: 'COMPLETED' as const,
        value: 10000,
        correlationID: 'charge_123',
        transactionID: 'tx_123',
        customer: {
          name: 'Test Customer',
          email: '<EMAIL>',
          taxID: {
            taxID: '12345678901',
            type: 'BR:CPF' as const,
          },
        },
      },
    };

    it('should process webhook payload successfully', async () => {
      await expect(
        service.handleWebhookPayload(mockPayload, 'test-signature'),
      ).resolves.not.toThrow();
    });

    it('should process charge webhook when charge is present', async () => {
      const processChargeWebhookSpy = jest.spyOn(
        service as any,
        'processChargeWebhook',
      );
      await service.handleWebhookPayload(mockPayload, 'test-signature');
      expect(processChargeWebhookSpy).toHaveBeenCalledWith(mockPayload.charge);
    });
  });
});
