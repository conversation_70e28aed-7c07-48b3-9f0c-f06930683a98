import { Injectable } from '@nestjs/common';
import { RegisterDto } from 'src/modules/auth/dto/register.dto';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import { Subscription } from 'src/modules/pagarme/types/pagarme.type';
import { Plan } from 'src/modules/plan/model/plan.model';
import { PlanMetadata } from 'src/modules/plan/types/metadata.type';
import { Gateway } from 'src/types/gateway.enum';
import { updateAccountFromMetadata } from 'src/utils/account.utils';
import { SubscriptionService } from '../subscription/subscription.service';
import { QISubscription } from '../subscription/types/qiplus.types';
import { AccountRepository } from './account.repository';
import { Account } from './model/account.model';
@Injectable()
export class AccountService {
  constructor(
    private readonly accountRepository: AccountRepository,
    private readonly subscriptionService: SubscriptionService,
  ) { }

  sanitized(account: Account): Partial<Account> {
    const {
      owner,
      active,
      config,
      planId,
      payment_status,
      affiliateId,
      parentId,
      modules,
    } = account;
    const { gateway } = payment_status || {};

    const subscription = account?.[gateway]
      ?.subscription as unknown as Subscription | null;

    return {
      owner,
      active,
      config,
      planId,
      payment_status,
      affiliateId,
      parentId,
      modules,
      subscription,
    } as Partial<Account>;
  }

  async create(
    uid: string,
    dto: RegisterDto,
    plan: Plan,
    pagarmeCustomerId: string,
    openpixCustomerId: string,
  ): Promise<DefaultResponse<FirebaseFirestore.DocumentReference | null>> {
    const shotx = dto.additionals.includes('shotx-module');
    const option = plan.option || plan.options[0];

    const { affiliateId, parentId } = dto;

    const account: Account = {
      owner: uid,
      active: false,
      config: {
        automations_included: 0,
        billing: true,
        campaigns_included: 0,
        custom_plan: plan.isCustomPlan || false,
        emails_included: 0,
        forms_included: 0,
        mailboxes_included: 0,
        mailing_qiplus_disabled: false,
        shotx_included: shotx ? 1 : 0,
        smtp_enabled: false,
        trial_days: 0,
        ...plan.config,
      },
      customer: {
        pagarme: pagarmeCustomerId,
        openpix: openpixCustomerId,
      },
      planId: dto.planId,
      post_type: 'account',
      data: option,
      levels: plan.levels,
      modules: {
        ...plan.modules,
        shotx: shotx || plan.modules.shotx,
      },
      payment_status: {
        status: 'pending_payment',
        error_message: '',
        gateway: Gateway.PAGARME,
        subscription_id: '',
        planId: dto.planId,
      },
      title: `${dto.name} - ${plan.name}`,
      type: dto.isCompany ? 'corporation' : 'individual',
      status: 'publish',
    };

    if (affiliateId) {
      account.affiliateId = affiliateId;
    }

    if (parentId) {
      account.parentId = parentId;
    }

    return await this.accountRepository.create(account);
  }

  async updateFromPlan(accountId: string, newPlan: Plan, metadata: PlanMetadata) {
    const configuration = updateAccountFromMetadata(newPlan, metadata);
    const account = await this.accountRepository.getAccount(accountId);
    if (!account) {
      throw new Error('Conta não encontrada');
    }
    await this.updateAccount(accountId, {
      config: {
        // Atualiza a quantidade de dos itens
        ...account?.config,
        ...configuration.config,
      },
      modules: {
        // Atualiza os módulos
        ...account?.modules,
        ...configuration.modules,
      },
      data: configuration.option, // Atualiza a quantidade de leads
    });
  }

  async updateFromSubscription(accountId: string, qiSubscription: QISubscription) {
    const account = await this.accountRepository.getAccount(accountId);
    if (!account) {
      throw new Error('Conta não encontrada');
    }
    await this.updateAccount(accountId, {
      planId: qiSubscription.planId,
      config: {
        // Atualiza a quantidade de dos itens
        ...account?.config,
        ...qiSubscription.accountConfig.config,
      },
      modules: {
        // Atualiza os módulos
        ...account?.modules,
        ...qiSubscription.accountConfig.modules,
      },
      data: qiSubscription.accountConfig.data, // Atualiza a quantidade de leads
    });
  }

  async updateAccount(accountId: string, data: any) {
    await this.accountRepository.update(accountId, data);
  }

  async getAccount(accountId: string) {
    return this.accountRepository.getAccount(accountId);
  }

  async getActiveSubscription(accountId: string) {
    if (!accountId) return null;
    return this.subscriptionService.getActiveSubscription(accountId);
  }
}
