import { FileText, Rocket, Shield } from "lucide-react";

export type PaymentStatus = "processing" | "success" | "error";
export type PaymentMethod = "credit_card" | "boleto" | "pix";

export interface PaymentResultModalProps {
    isOpen: boolean;
    onClose: () => void;
    status: PaymentStatus;
    paymentMethod: PaymentMethod;
    errorMessage?: string;
    boletoUrl?: string;
}

export const BENEFITS = [
    {
        icon: Rocket,
        title: "Acesso Instantâneo",
        description: "Plataforma liberada! Comece agora mesmo",
        type: "launch",
        animation: {
            launch: {
                y: -100,
                opacity: 0,
                transition: {
                    duration: 0.5,
                    ease: "easeOut"
                }
            }
        }
    },
    {
        icon: FileText,
        title: "PDF",
        description: "Download imediato do material completo em PDF",
        type: "scale",
        animation: {
            scale: {
                scale: 0.1,
                rotate: 360,
                opacity: 0,
                transition: {
                    duration: 0.7,
                    ease: "easeInOut"
                }
            }
        }
    },
    {
        icon: Shield,
        title: "Garantia Premium",
        description: "7 dias de garantia de satisfação",
        type: "shake",
        animation: {
            shake: {
                y: 10,
                opacity: 0,
                transition: {
                    duration: 0.7,
                    ease: "easeInOut"
                }
            }
        }
    }
] as const; 