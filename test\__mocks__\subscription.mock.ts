import { QIPaymentMethod, QISubscription, QISubscriptionStatus } from "src/modules/core/types";

export
  const mockSubscription: QISubscription = {
    id: 'subscription-123',
    accountId: 'account-123',
    planId: 'plan-123',
    status: QISubscriptionStatus.ACTIVE,
    billingInterval: 'yearly' as any,
    billingDay: 1,
    startDate: { toDate: () => new Date() } as any,
    currentPeriodStart: { toDate: () => new Date() } as any,
    currentPeriodEnd: { toDate: () => new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) } as any,
    nextBillingDate: { toDate: () => new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) } as any,
    currency: 'BRL',
    paymentMethod: QIPaymentMethod.CREDIT_CARD,
    installments: 1,
    items: [
      {
        id: 'item-123',
        name: 'Test Plan',
        type: 'plan',
        included: 100,
        quantity: 1,
        unitPrice: 99900,
        totalPrice: 99900,
      },
    ],
    accountConfig: {
      data: {
        contacts_max: 100,
        yearly_value: 99900,
        monthly_value: 9990,
        contacts_min: 100,
      },
      modules: {
        shotx: true,
      },
      config: {},
    },
    cycle: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    gateway: 'pagarme' as any,
    cardId: 'card-123',
    customerId: 'customer-123',
  };
