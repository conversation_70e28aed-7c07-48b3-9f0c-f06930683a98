import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/contexts/auth/useAuth";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { CPFInput } from "../forms/CPFInput";
import { EmailInput } from "../forms/EmailInput";
import { PhoneInput } from "../forms/PhoneInput";
import { InputMessageError } from "../InputMessageError";
import { CheckoutFormData } from "./types";
import { isOver, nowDate } from "@/lib/date.utils";
interface PersonalInfoFieldsProps {
  register: UseFormRegister<CheckoutFormData>;
  watch;
  errors: FieldErrors<CheckoutFormData>;
}

export function PersonalInfoFields({
  register,
  watch,
  errors,
}: PersonalInfoFieldsProps) {
  const { countryCodes, selectedCountry, setSelectedCountryCode } =
    useCheckout();

  const { hasUserData, isAuthenticated } = useAuth();

  return (
    <div className="grid gap-4">
      <div>
        <Label htmlFor="name">Nome Completo</Label>
        <Input
          // className={`input-class ${errors.name ? 'border-red-500' : ''}`}
          id="name"
          {...register("name", {
            required: {
              value: true,
              message: "Nome é obrigatório",
            },
          })}
          placeholder="Seu nome completo"
          disabled={hasUserData("name")}
        />
        <InputMessageError error={errors.name?.message} />
      </div>
      <EmailInput
        register={register}
        errors={errors}
        disabled={hasUserData("email")}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <CPFInput
            register={register}
            errors={errors}
            disabled={hasUserData("cpf")}
          />
        </div>
        <div>
          <div>
            <Label htmlFor="birthdate">Data de Nascimento</Label>
            <Input
              // className={`input-class ${errors.birthdate ? 'border-red-500' : ''}`}
              type="date"
              id="birthdate"
              {...register("birthdate", {
                required: {
                  value: true,
                  message: "Data de nascimento é obrigatória",
                },
                validate: (value) => {
                  if (!isOver(nowDate(value), 18)) {
                    return "Você deve ter pelo menos 18 anos";
                  }

                  if (isOver(nowDate(value), 200)) {
                    return "Você deve ter menos de 200 anos";
                  }

                  return true;
                },
                disabled: hasUserData("birthdate"),
              })}
              placeholder="00/00/0000"
            />
          </div>
          <InputMessageError error={errors.birthdate?.message} />
        </div>
      </div>

      <PhoneInput
        suitable={false}
        type="mobile"
        inputId="phone"
        selectId="countryCode"
        countryCodes={countryCodes}
        selectedCountry={selectedCountry}
        setSelectedCountryCode={setSelectedCountryCode}
        register={register}
        errors={errors}
        disabled={hasUserData("phone")}
      />
      {!isAuthenticated && (
        <div>
          <Label htmlFor="password">Senha</Label>
          <Input
            id="password"
            {...register("password", {
              required: {
                value: true,
                message: "Senha é obrigatória",
              },
              pattern: {
                // Validar se tem 6 dígitos apenas e se contem apenas letras e números
                value: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,}$/,
                // value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/,
                message:
                  "Senha inválida (deve conter pelo menos 6 caracteres, incluindo letras e números)",
              },
            })}
            type="password"
          />
          <InputMessageError error={errors.password?.message} />
        </div>
      )}
      {!isAuthenticated && (
        <div>
          <Label htmlFor="passwordConfirm">Confirmação da Senha</Label>
          <Input
            id="passwordConfirm"
            {...register("passwordConfirm", {
              required: {
                value: true,
                message: "Confirmação da senha é obrigatória",
              },
              validate: (value) => {
                if (value !== watch("password")) {
                  return "As senhas devem ser iguais";
                }

                return true;
              },
            })}
            type="password"
          />
          <InputMessageError error={errors.passwordConfirm?.message} />
        </div>
      )}
    </div>
  );
}
