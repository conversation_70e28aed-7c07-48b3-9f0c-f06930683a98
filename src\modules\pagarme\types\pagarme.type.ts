import { PagarmeSplit } from './pagarme.split.type';

export type PagarmeCustomerResponse = {
  id?: string;
  data?: Customer;
  paging?: {
    total: number;
  };
  response?: {
    message: string;
    errors: any[];
    request: any;
  };
  error?: boolean;
  message?: string;
  errors?: any[];
};

export type Customer = {
  id?: string;
  name: string;
  email: string;
  code: string;
  document: string;
  document_type: 'cpf' | 'cnpj';
  type: 'individual' | 'company';
  gender?: 'male' | 'female' | 'other';
  delinquent?: boolean;
  address?: Address;
  created_at?: string; // ISO Date string
  updated_at?: string; // ISO Date string
  birthdate?: string; // ISO Date string
  phones?: Record<string, unknown>; // Pode ajustar conforme a estrutura dos telefones
};

export type Address = {
  country: string;
  state: string;
  city: string;
  postalCode: string;
  line_1: string;
  line_2: string;
};

export type CreateCard = {
  id?: string;
  customer_id: string;
  number: string;
  holder_name: string;
  exp_month: string;
  exp_year: string;
  cvv: string;
  billing_address?: Address;
  options?: CardOptions;
};

export type CardOptions = {
  verify_card?: boolean;
};

export type PagarmeCardResponse = {
  id: string;
  first_six_digits: string;
  last_four_digits: string;
  brand: string;
  holder_name: string;
  exp_month: number;
  exp_year: number;
  status: 'active' | 'inactive';
  type: 'credit' | 'debit' | 'voucher';
  created_at: string; // ISO Date string
  updated_at: string; // ISO Date string
  billing_address: Address;
  customer: Customer;
};

export type GetCard = {
  customer_id: string;
  card_id: string;
};

export type Phones = {
  home_phone?: Phone;
};

export type Phone = {
  mobile_phone: {
    country_code: string;
    number: string;
    area_code: string;
  };
  home_phone?: {
    country_code: string;
    number: string;
    area_code: string;
  };
};

export interface PaymentSubscriptionResponse {
  error: boolean;
  errors: Array<{ message: string }> | [];
  message: string;
  isUpgrade?: boolean;
  skipPayment?: boolean;
  data?: any;
}

export interface SubscriptionResponse extends PaymentSubscriptionResponse {
  data: Subscription;
}

export interface SubscriptionsResponse extends PaymentSubscriptionResponse {
  data: Subscription[];
}

export type Subscription = {
  id: string;
  code: string;
  start_at: string;
  interval: 'month' | 'year';
  interval_count: number;
  billing_type: 'prepaid' | 'postpaid';
  current_cycle: CurrentCycle;
  next_billing_at: string;
  payment_method: 'credit_card' | 'boleto' | 'pix';
  currency: 'BRL';
  installments: number;
  status: 'active' | 'canceled' | 'failed' | 'future';
  created_at: string;
  updated_at: string;
  customer: Customer;
  card?: PagarmeCardResponse;
  plan: Plan;
  items: SubscriptionItem[];
  boleto?: Record<string, unknown>;
  metadata?: Record<string, unknown> & {
    planIdQIPLUS?: string;
  };
};

export type CurrentCycle = {
  id: string;
  start_at: string;
  end_at: string;
  billing_at: string;
  status: 'billed' | 'pending';
  cycle: number;
};

export type Plan = {
  id: string;
  name: string;
  description: string;
  url: string;
  interval: 'month' | 'year';
  interval_count: number;
  billing_type: 'prepaid' | 'postpaid';
  payment_methods: ('credit_card' | 'boleto' | 'pix')[];
  installments: number[];
  status: 'active' | 'inactive';
  currency: 'BRL';
  created_at: string;
  updated_at: string;
};

export type SubscriptionItem = {
  id: string;
  name: string;
  description: string;
  quantity: number;
  status: 'active' | 'canceled';
  created_at: string;
  updated_at: string;
  pricing_scheme: PricingScheme;
  cycles: number;
};

export type PricingScheme = {
  price: number;
  scheme_type: 'unit' | 'volume' | 'tiered';
};

export type Increment = {
  value: number;
  increment_type: 'percentage' | 'flat';
  cycle: string;
  subscription?: {
    metadata: {
      accountId: string;
    };
  };
};
export type SubscriptionRequest = {
  card?: {
    number: string;
    holder_name: string;
    exp_month: number;
    exp_year: number;
    cvv: string;
  };
  card_id?: string;
  installments: string;
  plan_id: string;
  payment_method: 'credit_card' | 'boleto' | 'pix';
  customer_id: string;
  metadata?: Record<string, unknown>;
  increments?: Increment[];
  split?: PagarmeSplit;
  start_at?: Date;
};

export type Invoice = {
  id: string;
  code: string;
  url: string;
  amount: number;
  total_discount: number;
  total_increment: number;
  status: 'pending' | 'paid' | 'failed' | 'canceled';
  payment_method: 'credit_card' | 'boleto' | 'pix';
  due_at: string;
  created_at: string;
  items: InvoiceItem[];
  customer: Customer;
  subscription: SubscriptionResponse;
  cycle: CurrentCycle;
  charge: Charge;
  metadata: Record<string, unknown>;
};

type Paging = {
  total: number;
};
export type InvoicesResponse = {
  data: Invoice[];
  paging: Paging;
  error: boolean;
  errors: [];
  message: string;
};

export type InvoiceResponse = {
  data: Invoice['id'];
  paging: Paging;
  error: boolean;
  errors: [];
  message: string;
};

export type InvoiceItem = {
  subscription_item_id: string;
  name: string;
  description: string;
  amount: number;
  quantity: number;
  pricing_scheme: {
    price: number;
    scheme_type: 'unit' | 'tiered';
  };
};

export type Charge = {
  id: string;
  code: string;
  amount: number;
  status: 'pending' | 'paid' | 'failed' | 'canceled';
  currency: 'BRL';
  payment_method: 'credit_card' | 'boleto' | 'pix';
  due_at: string;
  created_at: string;
  updated_at: string;
  last_transaction: Transaction;
  metadata: Record<string, unknown>;
};

export type Transaction = {
  id: string;
  transaction_type: 'credit_card' | 'boleto' | 'pix';
  amount: number;
  status: 'pending' | 'paid' | 'failed' | 'canceled';
  success: boolean;
  created_at: string;
  updated_at: string;
  url: string;
  gateway_response: {
    code: string;
    errors: { message: string }[];
  };
  antifraud_response: Record<string, unknown>;
  metadata: Record<string, unknown>;
};

export interface SubscriptionItemsResponse {
  error: boolean;
  errors: any[];
  message: string;
  data: {
    data: SubscriptionItem[];
    paging: {
      total: number;
    };
  };
}

export interface SubscriptionIncrementsResponse {
  error: boolean;
  errors: any[];
  message: string;
  data: {
    data: Increment[];
    paging: {
      total: number;
    };
  };
}

export type CardResponse = {
  id: string;
  first_six_digits: string;
  last_four_digits: string;
  brand: string;
  holder_name: string;
  exp_month: number;
  exp_year: number;
  status: 'active' | 'inactive';
  type: 'credit' | 'debit' | 'voucher';
  created_at: string; // ISO Date string
  updated_at: string; // ISO Date string
  billing_address: Address;
  customer: Customer;
};

export type CardsResponse = {
  error: boolean;
  errors: any[];
  message: string;
  data: CardResponse[];
  paging: {
    total: number;
  };
};
