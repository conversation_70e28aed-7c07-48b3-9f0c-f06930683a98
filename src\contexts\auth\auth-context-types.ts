import { CheckoutFormData } from '@/components/checkout/types';
import { Account } from '@/types/account';
import { QISubscription } from '@/types/backend/qiplus.types';
import { User } from 'firebase/auth';
import { ReactNode } from 'react';

export interface RegisterResponse {
  error: boolean;
  message: string;
  statusCode: number;
  token: string;
  uid: string;
  accountId: string;
  qiUserId: string;
}
export interface AuthContextData {
  user: User | null | undefined;
  userData: Partial<CheckoutFormData> | null;
  getUserData: (field: string) => string | null;
  hasUserData: (field: string) => boolean;
  account: Account | null;
  setAccount: (account: Account) => void;
  displayName: string | null;
  shortenDisplayName: () => string | null;
  register: (data) => Promise<RegisterResponse>;
  isAuthenticated: boolean | undefined;
  loginWithToken: (token: string) => Promise<User | null>;
  authenticate: () => Promise<{ token: string; account: Account } | null>;
  accessSystemUrl: string | null;
  signOut: () => Promise<void>;
  loading: boolean;
  subscription: QISubscription | null;
  setSubscription: (subscription: QISubscription) => void;
  getSubscription: () => Promise<QISubscription | null>;
  refreshSubscription: () => Promise<void>;
  isRegistering: boolean;
}

export interface AuthProviderProps {
  children: ReactNode;
}
