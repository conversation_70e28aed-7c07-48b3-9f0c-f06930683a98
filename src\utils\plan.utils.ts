import { Plan } from 'src/modules/plan/model/plan.model';

export function getPlanOption(plan: Plan, leadsCount: number) {
  return (
    plan.options.find(
      (option) =>
        leadsCount >= option.contacts_min && leadsCount <= option.contacts_max,
    ) || null
  );
}

export function convertPlanAmountToCents(plan: any) {
  const convertToCents = (value: any) => {
    if (typeof value === 'string') return Math.round(Number(value) * 100);
    if (typeof value === 'number') return Math.round(value * 100);
    return value;
  };

  const values = plan.values || {}
  const valuesInCents = Object.entries(values).reduce((acc, [key, value]) => {
    acc[key] = convertToCents(value);
    return acc;
  }, {});

  const options = plan.options || [];
  const optionsInCents = options.map((option) => {
    return {
      ...option,
      monthly_value: convertToCents(option.monthly_value),
      yearly_value: convertToCents(option.yearly_value),
    };
  });

  return {
    ...plan,
    values: valuesInCents,
    options: optionsInCents,
  };
}
