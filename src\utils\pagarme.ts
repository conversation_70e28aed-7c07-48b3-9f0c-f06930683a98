import { Customer } from 'src/modules/pagarme/types/pagarme.type';
import { CreatePaymentDto } from 'src/modules/payment/dto/create-payment.dto';

export function returnOnlyNumbers(str: string) {
  return str.replace(/\D/g, '');
}

export function splitNumbers(str: string) {
  return str.split(/[-\/]/).map(Number);
}
export function prepareCustomerData(data: any) {
  const { isCompany, email, birthdate, name, cpf, companyCnpj } =
    data as CreatePaymentDto;
  const document = isCompany ? companyCnpj! : cpf;
  const documentType = isCompany ? 'cnpj' : 'cpf';
  const customerType = isCompany ? 'company' : 'individual';

  const customer = {
    name: name,
    email: email,
    document: document,
    document_type: documentType,
    type: customerType,
    birthdate,
  } as Customer;

  return customer;
}
