import { CreatePaymentDto } from "src/modules/payment/dto/create-payment.dto";
import { PaymentMethod } from "src/modules/payment/enum/paymentMethod.enum";

export const mockCreatePaymentDto: Partial<CreatePaymentDto> = {
  accountId: 'account-123',
  planId: 'plan-123',
  paymentMethod: PaymentMethod.PIX,
  name: 'Test User',
  email: '<EMAIL>',
  cpf: '********901',
  birthdate: '1990-01-01',
  phone: '***********',
  isCompany: false,
  toBilling: false,
  leadsCount: 100,
  additionals: [],
  isYearly: true,
  installments: 1,
  // Required fields for CreatePaymentDto
  city: 'Test City',
  complement: 'Apt 123',
  neighborhood: 'Test Neighborhood',
  number: '123',
  postalCode: '********',
  state: 'TS',
  street: 'Test Street',
  country: 'BR',
  discount: 0,
  uid: 'user-123',
  userId: 'user-123',
  billingDay: 10,
  customFeatures: [],
  phoneCountryCode: '55',
};