
import { motion } from "framer-motion";

interface SupportFooterProps {
  itemAnimation: any;
}

export const SupportFooter = ({ itemAnimation }: SupportFooterProps) => {
  return (
    <motion.div
      variants={itemAnimation}
      className="text-center text-sm text-slate-500 pt-4"
    >
      <p>
        Precisa de ajuda?{" "}
        <a href="/support" className="bg-clip-text text-transparent bg-gradient-to-r from-[#e9177c] to-[#f16434] font-medium hover:opacity-80">
          Entre em contato com nosso suporte
        </a>
      </p>
    </motion.div>
  );
};
