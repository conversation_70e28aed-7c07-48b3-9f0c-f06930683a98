import axios, { AxiosInstance } from "axios";

// Armazenamento para o token que será atualizado pelos componentes React
export let authToken: string | null = null;

// Função para atualizar o token a partir de componentes React
export const setAuthToken = (token: string | null) => {
  authToken = token;
};

export class ApiService {
  protected readonly API_URL: string;
  protected readonly axios: AxiosInstance;

  constructor() {
    this.API_URL = import.meta.env.VITE_API_URL;
    this.axios = axios.create({
      baseURL: this.API_URL,
      headers: {
        "Content-Type": "application/json",
      },
    });
    this.axios.interceptors.request.use(
      (config) => {
        // Usa o token armazenado em variável global
        if (authToken) {
          config.headers.Authorization = `Bearer ${authToken}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }
}
