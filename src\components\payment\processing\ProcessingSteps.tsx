import { motion } from "framer-motion";
import { Check } from "lucide-react";
import { useEffect, useState } from "react";

interface ProcessingStepsProps {
    itemAnimation;
    onFinish: () => void;
}

const steps = [
    "Validando informações",
    "Processando pagamento",
    "Configurando sua conta",
    "Finalizando",
];
export const ProcessingSteps = ({ itemAnimation, onFinish }: ProcessingStepsProps) => {
    const [currentStep, setCurrentStep] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));
        }, 2000);

        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        if (currentStep === steps.length - 1) {
            onFinish();
        }
    }, [currentStep, onFinish]);

    return (
        <motion.div
            variants={itemAnimation}
            className="space-y-3 max-w-sm mx-auto"
        >
            {steps.map((step, index) => (
                <div
                    key={step}
                    className={`flex items-center space-x-3 p-2 rounded-lg transition-colors ${index === currentStep
                        ? "bg-primary/10 dark:bg-primary/10"
                        : index < currentStep
                            ? "bg-green-50 dark:bg-green-900"
                            : "bg-gray-50 dark:bg-gray-900"
                        }`}
                >
                    {index < currentStep ? (
                        <Check className="h-5 w-5 text-green-500 dark:text-green-100" />
                    ) : (
                        <div
                            className={`h-2 w-2 rounded-full ${index === currentStep ? "bg-primary" : "bg-gray-300"
                                }`}
                        />
                    )}
                    <span
                        className={`text-sm ${index === currentStep
                            ? "text-primary font-medium"
                            : index < currentStep
                                ? "text-green-600 dark:text-green-100"
                                : "text-gray-500 dark:text-gray-100"
                            }`}
                    >
                        {step}
                    </span>
                </div>
            ))}
        </motion.div>
    );
}; 