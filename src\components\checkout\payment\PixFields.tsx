import { UseFormRegister, FieldErrors } from "react-hook-form";
import { CheckoutFormData } from "../types";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { HelpCircle } from "lucide-react";

interface PixFieldsProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function PixFields({ register, errors }: PixFieldsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h4 className="text-sm font-medium">Dados do PIX</h4>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Você receberá um QR Code para pagamento após finalizar o pedido
              </p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="rounded-lg border border-muted p-4 bg-muted/10">
        <p className="text-sm text-muted-foreground">
          Após confirmar o pedido, você receberá um QR Code para realizar o
          pagamento via PIX. O pagamento será processado instantaneamente.
        </p>
      </div>
    </div>
  );
}
