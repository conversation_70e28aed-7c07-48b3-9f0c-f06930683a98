# Módulo OpenPix

Este módulo implementa a integração com a API da OpenPix para gerenciamento de assinaturas via PIX.

## Configuração

Adicione as seguintes variáveis ao seu arquivo `.env`:

```env
OPENPIX_API_KEY=sua_api_key
OPENPIX_BASE_URL=https://api.openpix.com.br/api/v1
OPENPIX_WEBHOOK_URL=https://sua-api.com/webhooks/openpix
OPENPIX_WEBHOOK_SECRET=seu_webhook_secret
```

## Uso

### Importação do Módulo

```typescript
import { OpenPixModule } from './modules/openpix/openpix.module';

@Module({
  imports: [
    OpenPixModule,
    // ...
  ],
})
export class AppModule {}
```

### Serviços Disponíveis

#### OpenPixCustomerService

Gerenciamento de clientes:

```typescript
@Injectable()
export class YourService {
  constructor(private readonly customerService: OpenPixCustomerService) {}

  async createCustomer() {
    const customer = await this.customerService.createCustomer({
      name: 'Cliente Exemplo',
      taxID: {
        taxID: '12345678901',
        type: 'BR:CPF',
      },
      email: '<EMAIL>',
    });
  }
}
```

#### OpenPixSubscriptionService

Gerenciamento de assinaturas:

```typescript
@Injectable()
export class YourService {
  constructor(
    private readonly subscriptionService: OpenPixSubscriptionService,
  ) {}

  async createSubscription() {
    const subscription = await this.subscriptionService.createSubscription({
      value: 10000, // R$ 100,00
      customer: {
        name: 'Cliente Exemplo',
        taxID: '12345678901',
        email: '<EMAIL>',
      },
      dayOfMonth: 10,
      interval: 'MONTHLY',
    });
  }
}
```

#### OpenPixWebhookService

Gerenciamento de webhooks:

```typescript
@Injectable()
export class YourService {
  constructor(private readonly webhookService: OpenPixWebhookService) {}

  async setupWebhook() {
    const webhook = await this.webhookService.registerWebhook({
      name: 'Webhook de Pagamentos',
      event: 'OPENPIX:CHARGE_COMPLETED',
      url: 'https://sua-api.com/webhooks/openpix',
      authorization: 'seu-webhook-secret',
      isActive: true,
    });
  }
}
```

## Webhooks

O módulo inclui um handler de webhooks que processa as notificações da OpenPix. Configure o endpoint no seu controller:

```typescript
@Controller('webhooks/openpix')
export class WebhookController {
  constructor(private readonly webhookService: OpenPixWebhookService) {}

  @Post()
  async handleWebhook(
    @Body() payload: OpenPixWebhookPayload,
    @Headers('x-webhook-signature') signature: string,
  ) {
    await this.webhookService.handleWebhookPayload(payload, signature);
  }
}
```

## Testes

Para rodar os testes do módulo:

```bash
# Testes unitários
npm run test src/modules/openpix

# Cobertura de testes
npm run test:cov src/modules/openpix
```

## Tipos de Eventos Webhook

- `OPENPIX:CHARGE_CREATED`: Nova cobrança criada
- `OPENPIX:CHARGE_COMPLETED`: Cobrança concluída
- `OPENPIX:CHARGE_EXPIRED`: Cobrança expirada
- `OPENPIX:TRANSACTION_RECEIVED`: Nova transação PIX recebida
- `OPENPIX:TRANSACTION_REFUND_RECEIVED`: Reembolso de transação recebido

## Tratamento de Erros

O módulo inclui tratamento de erros para as principais operações. Os erros são logados e podem ser capturados usando try/catch:

```typescript
try {
  await this.subscriptionService.createSubscription(dto);
} catch (error) {
  if (error instanceof AxiosError) {
    // Trate erros da API
  }
  throw error;
}
```
