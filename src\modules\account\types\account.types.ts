// Tipos auxiliares para reutilização
interface Metadata {
  accountId: string;
  implementation: string;
  implementation_id: string;
  parcelas: string;
  uid: string;
}

interface Customer {
  id: string;
  name: string;
  email: string;
  code: string;
  document: string;
  document_type: string;
  type: 'individual';
  delinquent: boolean;
  created_at: string;
  updated_at: string;
  birthdate: string;
  phones: Record<string, unknown>; // Pode ser expandido se os telefones tiverem estrutura fixa
}

interface PricingScheme {
  price: number;
  scheme_type: string;
}

interface PlanItem {
  id: string;
  name: string;
  description: string;
  quantity: number;
  status: string;
  created_at: string;
  updated_at: string;
  pricing_scheme: PricingScheme;
}

interface Cycle {
  id: string;
  start_at: string;
  end_at: string;
  billing_at: string;
  status: string;
  cycle: number;
}

interface Plan {
  id: string;
  name: string;
  description: string;
  url: string;
  minimum_price: number;
  interval: string;
  interval_count: number;
  billing_type: string;
  payment_methods: string[];
  installments: number[];
  status: string;
  currency: string;
  created_at: string;
  updated_at: string;
}

interface BoletoItem {
  name: string;
  description: string;
  amount: number;
  quantity: number;
}

interface BoletoCharge {
  id: string;
  code: string;
  amount: number;
  status: string;
  currency: string;
  payment_method: string;
  due_at: string;
  created_at: string;
  updated_at: string;
  metadata: Metadata;
}

interface BoletoSubscription {
  id: string;
  code: string;
  start_at: string;
  interval: string;
  interval_count: number;
  billing_type: string;
  next_billing_at: string;
  payment_method: string;
  currency: string;
  installments: number;
  minimum_price: number;
  status: string;
  created_at: string;
  updated_at: string;
  metadata: Metadata;
}

interface BoletoData {
  id: string;
  code: string;
  url: string;
  amount: number;
  total_discount: number;
  total_increment: number;
  status: string;
  payment_method: string;
  due_at: string;
  created_at: string;
  items: BoletoItem[];
  customer: Customer;
  subscription: BoletoSubscription;
  cycle: Cycle;
  charge: BoletoCharge;
  metadata: Metadata;
}

interface SubscriptionData {
  id: string;
  code: string;
  start_at: string;
  interval: string;
  interval_count: number;
  billing_type: string;
  current_cycle: Cycle;
  next_billing_at: string;
  payment_method: string;
  currency: string;
  installments: number;
  minimum_price: number;
  status: string;
  created_at: string;
  updated_at: string;
  customer: Customer;
  plan: Plan;
  items: PlanItem[];
  boleto: {
    data: BoletoData[];
    paging: { total: number };
  };
  metadata: Metadata;
}

interface PagarmeSubscriptionResponse {
  errors: any[];
  error: boolean;
  message: string;
  data: SubscriptionData;
}

interface Config {
  automations_included: number;
  billing: boolean;
  campaigns_included: number;
  custom_plan: boolean;
  emails_included: number;
  forms_included: number;
  mailboxes_included: number;
  mailing_qiplus_disabled: boolean;
  shotx_included: number;
  smtp_enabled: boolean;
  payment_method: string;
  trial_days: number;
}

interface DataPlan {
  contacts_min: number;
  contacts_max: number;
  monthly_value: number;
  yearly_value: number;
}

interface Levels {
  automations: number;
  tickets: number;
  'landing-pages': number;
  mailing: number;
  questionnaires: number;
  raffles: number;
  barcode_products: number;
  campaigns: number;
  trackings: number;
  funnels: number;
  live_qiplus: number;
  events: number;
  forms: number;
}

interface Modules {
  automations: boolean;
  tickets: boolean;
  qiusers: boolean;
  mailboxes: boolean;
  contracts: boolean;
  'landing-pages': boolean;
  mailing: boolean;
  questionnaires: boolean;
  raffles: boolean;
  barcode_products: boolean;
  campaigns: boolean;
  trackings: boolean;
  live_qiplus: boolean;
  funnels: boolean;
  shotx: boolean;
  forms: boolean;
  events: boolean;
}

// Tipo principal do retorno da API
export interface AccountResponse {
  error: boolean;
  message: string;
  data: {
    owner: string;
    active: boolean;
    config: Config;
    planId: string;
    post_type: string;
    data: DataPlan;
    levels: Levels;
    modules: Modules;
    title: string;
    type: string;
    status: string;
    date: string;
    createdAt: number;
    keywords: string[];
    modified: string;
    ID: string;
    collection: string;
    id: string;
    locale: string;
    pagarme: {
      subscription: PagarmeSubscriptionResponse;
      customerId: string;
    };
    accountId: string;
    updatedAt: number;
    affiliateId?: string;
    parentId?: string;
  };
}
