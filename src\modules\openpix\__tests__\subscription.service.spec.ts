import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';
import { CreateSubscriptionDto } from '../dtos/subscription.dto';
import { OpenPixSubscriptionService } from '../services/subscription.service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('OpenPixSubscriptionService', () => {
  let service: OpenPixSubscriptionService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn((key: string) => {
      switch (key) {
        case 'openpix.apiKey':
          return 'test-api-key';
        case 'openpix.baseUrl':
          return 'https://api.openpix.com.br/api/v1';
        default:
          return null;
      }
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpenPixSubscriptionService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<OpenPixSubscriptionService>(
      OpenPixSubscriptionService,
    );
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSubscription', () => {
    const createSubscriptionDto: CreateSubscriptionDto = {
      value: 10000, // R$ 100,00
      customer: {
        name: 'Test Customer',
        taxID: '12345678901',
        email: '<EMAIL>',
      },
      dayOfMonth: 10,
      interval: 'MONTHLY',
      chargeType: 'PIX',
    };

    const mockResponse = {
      subscription: {
        id: 'sub_123',
        value: 10000,
        customer: {
          name: 'Test Customer',
          taxID: '12345678901',
          email: '<EMAIL>',
        },
        dayOfMonth: 10,
        interval: 'MONTHLY',
        chargeType: 'PIX',
        status: 'ACTIVE',
        createdAt: '2024-03-01T00:00:00Z',
        updatedAt: '2024-03-01T00:00:00Z',
      },
    };

    it('should create a subscription successfully', async () => {
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockResolvedValue({ data: mockResponse }),
      } as any);

      const result = await service.createSubscription(createSubscriptionDto);
      expect(result).toEqual(mockResponse);
    });

    it('should throw an error when creation fails', async () => {
      const error = new Error('Failed to create subscription');
      mockedAxios.create.mockReturnValue({
        post: jest.fn().mockRejectedValue(error),
      } as any);

      await expect(
        service.createSubscription(createSubscriptionDto),
      ).rejects.toThrow(error);
    });
  });
});
