import { Module } from '@nestjs/common';
import { PagarmeModule } from 'src/modules/pagarme/pagarme.module';
import { AffiliateRepository } from './affiliate.repository';
import { AffiliateService } from './affiliate.service';

@Module({
  imports: [PagarmeModule],
  providers: [AffiliateRepository, AffiliateService],
  exports: [AffiliateRepository, AffiliateService],
})
export class AffiliateModule {}
