import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON>ooter,
  <PERSON><PERSON>eader,
} from "@/components/ui/card";

export function PlanCardSkeleton() {
  return (
    <Card className="relative flex flex-col gap-4 p-4 rounded-lg shadow-lg h-full">
      {/* Discount badge skeleton */}
      <div className="absolute -top-3 right-4 h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>

      <CardHeader className="space-y-2">
        {/* Image skeleton */}
        <div className="mb-4 -mt-2">
          <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
        </div>

        <div className="space-y-2 flex flex-col items-center">
          {/* Title skeleton */}
          <div className="h-6 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>

          {/* Badge skeleton */}
          <div className="mt-1">
            <div className="h-5 w-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Description skeleton */}
        <div className="mt-2 h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </CardHeader>

      <CardContent className="flex-grow">
        {/* Price skeleton */}
        <div className="mb-4">
          <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
          <div className="h-8 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          <div className="h-4 w-40 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mt-2"></div>
        </div>

        {/* Leads control skeleton */}
        <div className="mb-4">
          <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>

        {/* Features skeleton */}
        <ul className="space-y-2">
          {[...Array(5)].map((_, index) => (
            <li key={index} className="flex items-center gap-2">
              <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              <div className="h-4 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </li>
          ))}
        </ul>
      </CardContent>

      <CardFooter className="flex flex-col gap-2 mt-auto">
        {/* Customize button skeleton */}
        <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>

        {/* Select button skeleton */}
        <div className="h-10 w-full bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>

        {/* Security badges skeleton */}
        <div className="flex justify-center mt-2">
          <div className="h-6 w-full bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </CardFooter>
    </Card>
  );
}
