import { Injectable } from '@nestjs/common';
import * as admin from 'firebase-admin';
import { DefaultResponse } from 'src/modules/firebase/types/response.type';
import { ResponseUtil } from 'src/utils/response';
import { Account } from './model/account.model';

@Injectable()
export class AccountRepository {
  private firestore = admin.firestore();
  private collection = this.firestore.collection('accounts');

  async create(
    account: Account,
  ): Promise<DefaultResponse<admin.firestore.DocumentReference | null>> {
    const result = await this.collection
      .add(account)
      .then((account) => ResponseUtil.success('Account created', account))
      .catch((error) => ResponseUtil.error(error.message));
    if (result.error || !result.data?.id) {
      console.error('Error creating account:', result);
      return result
    }

    await this.update(result.data.id, { id: result.data.id })
    return result
  }

  async getAccount(accountId: string): Promise<Account | null> {
    const doc = await this.collection.doc(accountId).get();
    return doc.data() as Account | null;
  }

  async findByUid(uid: string): Promise<FirebaseFirestore.DocumentData | null> {
    const doc = await this.collection.doc(uid).get();
    return doc.data() || null;
  }

  async update(id: string, data: any): Promise<void> {
    await this.collection.doc(id).update(data, { merge: true });
  }

  async getAccounts(): Promise<Account[]> {
    const accounts = await this.collection.get();
    return accounts.docs.map((doc) => doc.data() as Account);
  }
}
