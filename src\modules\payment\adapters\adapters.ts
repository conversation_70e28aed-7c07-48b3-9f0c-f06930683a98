import { QISubscription } from 'src/modules/core/types';
import { PaymentMethod } from '../enum/paymentMethod.enum';
import { PagarmeAdapter } from './pagarme.adapter';

export const PaymentAdapterByPaymentMethod = {
  [PaymentMethod.PIX]: PagarmeAdapter,
  [PaymentMethod.CREDIT_CARD]: PagarmeAdapter,
  [PaymentMethod.BOLETO]: PagarmeAdapter,
};

export interface PaymentAdapter {
  toCreateCustomer(data: any): any;
  toUpdateCustomer(data: any): any;
  toGetCustomer(data: any): any;
  fromGetCustomer(data: any): any;
  toSubscription(data: any): any;
  toQiSubscription(data: any): Omit<QISubscription, 'id'>;
  toCard(data: any): any;
  toOrder(data: any): any;
}
