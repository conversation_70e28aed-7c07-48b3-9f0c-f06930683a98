import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError, AxiosInstance } from 'axios';
import { DebugService } from 'src/debug/debug.service';
import {
  OpenPixWebhook,
  OpenPixWebhookResponse,
} from '../interfaces/webhook.interface';
import { WebhookPostBody } from '../types/webhook.types';
import { hmacVerifySignature } from '../utils/hmac.utils';
@Injectable()
export class OpenPixWebhookService {
  private readonly logger = new Logger(OpenPixWebhookService.name);
  private readonly api: AxiosInstance;

  constructor(
    private readonly configService: ConfigService,
    private readonly debugService: DebugService,
  ) {
    this.api = axios.create({
      baseURL: this.configService.get<string>('openpix.baseUrl'),
      headers: {
        Authorization: this.configService.get<string>('openpix.apiKey'),
      },
    });
  }

  async registerWebhook(
    webhook: OpenPixWebhook,
  ): Promise<OpenPixWebhookResponse> {
    try {
      const { data } = await this.api.post<OpenPixWebhookResponse>('/webhook', {
        webhook: {
          ...webhook,
          url:
            webhook.url || this.configService.get<string>('openpix.webhookUrl'),
          authorization:
            webhook.authorization ||
            this.configService.get<string>('openpix.webhookSecret'),
        },
      });
      return data;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error registering webhook:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async deleteWebhook(id: string): Promise<void> {
    try {
      await this.api.delete(`/webhook/${id}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error deleting webhook:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async listWebhooks(): Promise<OpenPixWebhookResponse[]> {
    try {
      const { data } = await this.api.get<{
        webhooks: OpenPixWebhookResponse[];
      }>('/webhook');
      return data.webhooks;
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(
          'Error listing webhooks:',
          error.response?.data || error.message,
        );
      }
      throw error;
    }
  }

  async handleWebhookPayload(
    body: WebhookPostBody,
    webhookSecret: string,
    signature?: string,
  ): Promise<void> {
    console.log('webhookSecret', webhookSecret);
    console.log('signature', signature);
    this.logger.debug('Processing webhook payload', body);
    this.debugService.sendWebhook(body);

    // Validate webhook test payload
    if (!body.charge && !body.pix) {
      this.logger.debug('Test webhook received');
      return;
    }

    // Validate HMAC signature
    if (!hmacVerifySignature(webhookSecret, JSON.stringify(body), signature)) {
      this.logger.error('Invalid HMAC signature');
      throw new UnauthorizedException('Invalid HMAC signature');
    }

    // Process the webhook payload
    if (body.charge?.status === 'COMPLETED') {
      await this.processCompletedCharge(body.charge);
    }
  }

  private async processCompletedCharge(
    charge: WebhookPostBody['charge'],
  ): Promise<void> {
    this.logger.debug('Processing completed charge', charge);

    // Here you would implement your business logic
    // For example, updating a donation status in your database
    // Similar to the example code's donation update logic
  }
}
