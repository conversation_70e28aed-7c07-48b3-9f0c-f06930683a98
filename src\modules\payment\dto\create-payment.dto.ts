import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsString,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { FeatureDto } from 'src/modules/auth/dto/feature.dto';
import { PaymentMethod } from '../enum/paymentMethod.enum';
import { MAX_TIMEZONE_DIFF } from 'src/utils/date.utils';

export class CreatePaymentDto {
  @IsString()
  accountId: string;

  // VALIDATIONS DATA
  @IsBoolean()
  toBilling: boolean;

  @IsBoolean()
  @IsNotEmpty()
  isCompany: boolean;

  // CUSTOMER DATA
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  cpf: string;

  @IsNumber({}, { message: 'Quantidade de leads deve ser um número' })
  @IsNotEmpty({ message: 'Quantidade de leads é obrigatória' })
  leadsCount: number;

  @IsArray()
  @IsString({ each: true })
  additionals: string[];

  @ValidateIf((o: CreatePaymentDto) => o.isCompany === true)
  @IsString()
  companyCnpj?: string;

  @IsString()
  @IsNotEmpty()
  birthdate: string;

  // BILLING ADDRESS DATA
  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingCity?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingComplement?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingNeighborhood?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingNumber?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingPostalCode?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingState?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingStreet?: string;

  @ValidateIf((o: CreatePaymentDto) => o.toBilling === false)
  @IsString()
  billingCountry?: string;

  // ADDRESS DATA
  @IsString()
  @IsNotEmpty()
  city: string;

  @IsString()
  @IsNotEmpty()
  complement: string;

  @IsString()
  @IsNotEmpty()
  neighborhood: string;

  @IsString()
  @IsNotEmpty()
  number: string;

  @IsString()
  @IsNotEmpty()
  postalCode: string;

  @IsString()
  @IsNotEmpty()
  state: string;

  @IsString()
  @IsNotEmpty()
  street: string;

  @IsString()
  @IsNotEmpty()
  country: 'BR';

  // CARD DATA
  @ValidateIf(
    (o: CreatePaymentDto) =>
      o.paymentMethod === PaymentMethod.CREDIT_CARD &&
      !o.cardHolderName &&
      !o.cardNumber &&
      !o.cardExpiry &&
      !o.cardCvc,
  )
  @IsString()
  cardId?: string;

  @ValidateIf(
    (o: CreatePaymentDto) =>
      o.paymentMethod === PaymentMethod.CREDIT_CARD && !o.cardId,
  )
  @IsString()
  cardHolderName?: string;

  @ValidateIf(
    (o: CreatePaymentDto) =>
      o.paymentMethod === PaymentMethod.CREDIT_CARD && !o.cardId,
  )
  @IsString()
  cardNumber?: string;

  @ValidateIf(
    (o: CreatePaymentDto) =>
      o.paymentMethod === PaymentMethod.CREDIT_CARD && !o.cardId,
  )
  @IsString()
  @IsNotEmpty()
  cardExpiry?: string;

  @ValidateIf(
    (o: CreatePaymentDto) =>
      o.paymentMethod === PaymentMethod.CREDIT_CARD && !o.cardId,
  )
  @IsString()
  @IsNotEmpty()
  cardCvc?: string;

  @IsNumber()
  @IsNotEmpty()
  installments: number;

  // OTHER DATA
  @IsNumber()
  @IsNotEmpty()
  discount: number;

  @IsString()
  @IsNotEmpty()
  @IsEnum(PaymentMethod, {
    message: 'O pagamento deve ser credit_card ou boleto',
  })
  paymentMethod: PaymentMethod;

  @IsString()
  phone: string;

  @IsString()
  phoneCountryCode: string;

  @IsString()
  planId: string;

  @IsString()
  @IsNotEmpty()
  uid: string;

  @IsString()
  userId: string;

  @IsBoolean()
  isYearly: boolean;

  // NOVA PROPRIEDADE: customFeatures
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FeatureDto)
  customFeatures: FeatureDto[];

  @IsNumber()
  @IsNotEmpty()
  billingDay: number;

  @IsString()
  @IsNotEmpty()
  clientTimestamp: string;
}
