import { OrderRequest } from 'src/modules/pagarme/types/pagarme.order.type';
import { QISubscription } from 'src/modules/core/types';
import { Gateway } from 'src/types/gateway.enum';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { onlyNumbers } from '../utils/helpers';
import { prepareBillingAddress } from '../utils/payment.utils';
import { PaymentAdapter } from './adapters';

export class PagarmeAdapter implements PaymentAdapter {
  toSubscription(data: any) {
    return data;
  }
  toCard(data: any) {
    return data;
  }
  toOrder(data: OrderRequest): OrderRequest {
    return data;
  }
  toCreateCustomer(data: CreatePaymentDto) {
    const document = (data.isCompany ? data.companyCnpj : data.cpf) || '';
    const cc = data.phoneCountryCode?.replace('+', '') || '55';
    const phone = data.phone.replace(/[^0-9]/g, '');
    const ac = phone.slice(0, 2);
    const n = phone.slice(2);
    return {
      name: data.name,
      email: data.email,
      document: onlyNumbers(document),
      document_type: data.isCompany ? 'cnpj' : 'cpf',
      type: data.isCompany ? 'company' : 'individual',
      code: data.uid,
      birthdate: data.birthdate,
      address: prepareBillingAddress(data),
      phones: {
        home_phone: {
          country_code: cc,
          area_code: ac,
          number: n,
        },
        mobile_phone: {
          country_code: cc,
          area_code: ac,
          number: n,
        },
      },
    };
  }
  toUpdateCustomer(data: any) {
    return this.toCreateCustomer(data);
  }
  toGetCustomer(data: any) {
    return {
      name: data.name,
      email: data.email,
      document: data.document,
      type: data.type,
    };
  }
  fromGetCustomer(data: any) {
    return {
      id: data.id,
      correlationID: data.code,
    };
  }
  toQiSubscription(data: any): Omit<QISubscription, 'id'> {
    return {
      gateway: Gateway.PAGARME,
      accountId: data.accountId,
      planId: data.planId,
      status: data.status,
      createdAt: data.createdAt,
      billingInterval: data.billingInterval,
      billingDay: data.billingDay,
      startDate: data.startDate,
      cycle: data.cycle,
      installments: data.installments,
      currentPeriodStart: data.currentPeriodStart,
      currentPeriodEnd: data.currentPeriodEnd,
      items: [],
      paymentMethod: data.paymentMethod,
      nextBillingDate: data.nextBillingDate,
      updatedAt: data.updatedAt,
      currency: 'BRL',
      accountConfig: {
        data: {
          contacts_max: 0,
          yearly_value: 0,
          monthly_value: 0,
          contacts_min: 0,
        },
        modules: {
          shotx: false,
        },
        config: {},
      },
    };
  }
}
