import { Additional } from "@/types/additional-types";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { AdditionalServicesStep as AdditionalServicesStepComponent } from "../AdditionalServicesStep";
import { CheckoutFormData } from "../types";

interface AdditionalServicesStepProps {
  title: string;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  additionals: Additional[];
  onAdditionalsChange: (additionals: Additional[]) => void;
}

export function AdditionalServicesStep({
  title,
  register,
  errors,
  additionals,
  onAdditionalsChange,
}: AdditionalServicesStepProps) {
  return (
    <AdditionalServicesStepComponent
      title={title}
      register={register}
      errors={errors}
      additionals={additionals}
      onAdditionalsChange={onAdditionalsChange}
    />
  );
}