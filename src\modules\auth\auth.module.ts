import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AccountsModule } from 'src/modules/account/account.module';
import { FirebaseModule } from 'src/modules/firebase/firebase.module';
import { OpenPixModule } from 'src/modules/openpix/openpix.module';
import { PagarmeModule } from 'src/modules/pagarme/pagarme.module';
import { PlanModule } from 'src/modules/plan/plan.module';
import { QiUsersModule } from 'src/modules/qiuser/qiuser.module';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { ServiceAuthGuard } from './service-auth.guard';

@Module({
  imports: [
    ConfigModule,
    FirebaseModule,
    AccountsModule,
    QiUsersModule,
    PlanModule,
    PagarmeModule,
    OpenPixModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, ServiceAuthGuard],
  exports: [AuthService, ServiceAuthGuard],
})
export class AuthModule { }
