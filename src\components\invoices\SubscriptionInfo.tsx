import { X } from "lucide-react";
import InfoCard from "@/components/checkout/InfoCard";
import SliderCard from "@/components/InfoSliderCard";
import { PlanItem } from "@/types/invoice.types";
import { QISubscription } from "@/types/backend/qiplus.types";
import { ItemCard } from "../ItemCard";

interface SubscriptionInfoProps {
  subscription: QISubscription;
  loading: boolean;
  infoCardData: Array<{ label: string; value: string | React.ReactNode }>;
  additionalPlanItems: PlanItem[];
  onCancelClick: () => void;
  sliderSettings: any;
  isActive: boolean;
}

const SubscriptionInfo = ({
  subscription,
  loading,
  infoCardData,
  additionalPlanItems,
  onCancelClick,
  sliderSettings,
  isActive,
}: SubscriptionInfoProps) => {
  if (loading) {
    return (
      <div className="mx-auto grid grid-cols-2 gap-4">
        <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
          <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
        </div>
        <div className="animate-pulse p-4 bg-gray-200 rounded-lg">
          <div className="h-6 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const isCancelled = subscription?.status === "canceled";
  const isScheduledCancel = subscription?.scheduledAction === "cancel";

  return (
    <div className="mx-auto grid grid-cols-2 gap-4">
      {/* Card Plano Atual + Botão de Cancelar Assinatura */}
      <div className="relative">
        <InfoCard
          title="Assinatura"
          items={infoCardData}
        />
        {isActive && !isCancelled && !isScheduledCancel && (
          <button
            onClick={onCancelClick}
            title="Cancelar Assinatura"
            className="absolute top-3 right-3 text-red-500 hover:opacity-75 focus:outline-none"
          >
            <X size={20} />
          </button>
        )}
      </div>

      {/* Itens do Plano */}
      <SliderCard
        title="Itens do Plano"
        sliderSettings={sliderSettings}
        items={additionalPlanItems}
        renderItem={(item) => <ItemCard item={item} />}
        status={subscription?.status}
      />
    </div>
  );
};

export default SubscriptionInfo;
