import { PaginationDto } from 'src/modules/pagarme/dto/pagination.dto';

// Interface para definir os tipos dos métodos de cada gateway
export interface GatewayTypes {
  // Customer
  listCostumersIn: PaginationDto;
  listCostumersOut: any;
  createCustomerIn: any;
  createCustomerOut: any;
  updateCustomerIn: any;
  updateCustomerOut: any;
  getCustomerIn: any;
  getCustomerOut: any;
  findCustomerDocumentIn: any;
  findCustomerDocumentOut: any;
  findCustomerByUidIn: any;
  findCustomerByUidOut: any;
  findCustomerIdByUidIn: any;
  findCustomerIdByUidOut: any;

  // Card
  listCardsIn: any;
  listCardsOut: any;
  createCardIn: any;
  createCardOut: any;
  deleteCardIn: any;
  deleteCardOut: any;
  getCardIn: any;
  getCardOut: any;

  // Subscription
  listSubscriptionsIn: any;
  listSubscriptionsOut: any;
  createSubscriptionIn: any;
  createSubscriptionOut: any;
  getSubscriptionIn: any;
  getSubscriptionOut: any;
  updateSubscriptionIn: any;
  updateSubscriptionOut: any;
  cancelSubscriptionIn: any;
  cancelSubscriptionOut: any;
  updateSubscriptionMetadataIn: any;
  updateSubscriptionMetadataOut: any;

  // Recurrence
  enableManualBillingIn: any;
  enableManualBillingOut: any;
  disableManualBillingIn: any;
  disableManualBillingOut: any;

  // Invoice
  listInvoicesIn: any;
  listInvoicesOut: any;
  getInvoiceIn: any;
  getInvoiceOut: any;

  // Recurrence Item
  getSubscriptionItemsIn: any;
  getSubscriptionItemsOut: any;
  getSubscriptionIncrementsIn: any;
  getSubscriptionIncrementsOut: any;

  // Order
  createOrderIn: any;
  createOrderOut: any;
  cancelOrderIn: any;
  cancelOrderOut: any;

  // Recipient
  listRecipientsIn: any;
  listRecipientsOut: any;

  // Split
  editSplitIn: any;
  editSplitOut: any;
}

export interface Gateway<T extends GatewayTypes> {
  // Customer
  listCostumers(pagination?: PaginationDto): Promise<T['listCostumersOut']>;
  createCustomer(
    customerData: T['createCustomerIn'],
  ): Promise<T['createCustomerOut']>;
  updateCustomer(
    customerId: string,
    customerData: T['updateCustomerIn'],
  ): Promise<T['updateCustomerOut']>;
  getCustomer(id: string): Promise<T['getCustomerOut']>;
  findCustomerDocument(document: string): Promise<T['findCustomerDocumentOut']>;
  findCustomerByUid(uid: string): Promise<T['findCustomerByUidOut']>;
  findCustomerIdByUid(uid: string): Promise<T['findCustomerIdByUidOut']>;

  // Card
  listCards(
    customerId: string,
    pagination?: PaginationDto,
  ): Promise<T['listCardsOut']>;
  createCard(cardData: T['createCardIn']): Promise<T['createCardOut']>;
  deleteCard(customerId: string, cardId: string): Promise<T['deleteCardOut']>;
  getCard(customerId: string, cardId: string): Promise<T['getCardOut']>;

  // Subscription
  listSubscriptions(
    customerId: string,
    pagination?: PaginationDto,
  ): Promise<T['listSubscriptionsOut']>;
  createSubscription(
    subscriptionData: T['createSubscriptionIn'],
  ): Promise<T['createSubscriptionOut']>;
  getSubscription(subscriptionId: string): Promise<T['getSubscriptionOut']>;
  updateSubscription(
    subscriptionId: string,
    subscriptionData: T['updateSubscriptionIn'],
  ): Promise<T['updateSubscriptionOut']>;
  cancelSubscription(
    subscriptionId: string,
  ): Promise<T['cancelSubscriptionOut']>;
  updateSubscriptionMetadata(
    subscriptionId: string,
    metadata: T['updateSubscriptionMetadataIn'],
  ): Promise<T['updateSubscriptionMetadataOut']>;

  // Recurrence
  enableManualBilling(
    subscriptionId: string,
  ): Promise<T['enableManualBillingOut']>;
  disableManualBilling(
    subscriptionId: string,
  ): Promise<T['disableManualBillingOut']>;

  // Invoice
  listInvoices(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<T['listInvoicesOut']>;
  getInvoice(invoiceId: string): Promise<T['getInvoiceOut']>;

  // Recurrence Item
  getSubscriptionItems(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<T['getSubscriptionItemsOut']>;
  getSubscriptionIncrements(
    subscriptionId: string,
    pagination?: PaginationDto,
  ): Promise<T['getSubscriptionIncrementsOut']>;

  // Order
  createOrder(orderData: T['createOrderIn']): Promise<T['createOrderOut']>;
  cancelOrder(orderId: string): Promise<T['cancelOrderOut']>;

  // Recipient
  listRecipients(pagination?: PaginationDto): Promise<T['listRecipientsOut']>;

  // Split
  editSplit(subscriptionId: string, split: any): Promise<T['editSplitOut']>;
}
